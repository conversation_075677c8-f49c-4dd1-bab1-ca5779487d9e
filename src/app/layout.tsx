import { type Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { AuthProvider } from "@/lib/auth-context";
import Navigation from "@/components/layout/navigation";
import Footer from "@/components/layout/footer";
import Socialbar from "@/components/layout/socialbar";
import { ThemeProvider } from "@/components/ThemeProvider";
import "@/styles/globals.css";
import React from "react";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Parallax Interactive | Game Development Studio",
  description:
    "Creating immersive gaming experiences that push the boundaries of imagination",
  /*openGraph: {
    images: "/images/socials/parallax.png", // Keep this for social sharing later
  },*/
  icons: {
    icon: [
      { url: "/logo.svg", sizes: "32x32", type: "image/ico" },
      { url: "/icon.png", sizes: "192x192", type: "image/png" },
      { url: "/logo.svg", sizes: "512x512", type: "image/png" },
    ],
    apple: { url: "/logo.svg", sizes: "180x180", type: "image/png" },
    shortcut: "/logo.svg",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="dark">
      <body
        className={`${inter.className} my-10 min-h-screen scroll-smooth bg-primary text-primary`}
      >
        <ThemeProvider attribute="class" enableSystem>
          <AuthProvider>
            <div className="flex min-h-screen flex-col">
              <Navigation />
              <Socialbar />
              <main className="min-h-screen flex-grow pt-16">{children}</main>
              <Footer />
            </div>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
