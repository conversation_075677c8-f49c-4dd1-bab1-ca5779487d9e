"use client";

import Image from "next/image";
import { useState } from "react";

export default function ContactPage() {
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccessMessage("");
    setErrorMessage("");

    if (!name || !email || !subject || !message) {
      setErrorMessage("Please fill in all required fields.");
      setLoading(false);
      return;
    }

    try {
      const formData = new FormData();
      formData.append("name", name);
      formData.append("email", email);
      formData.append("subject", subject);
      formData.append("message", message);

      const response = await fetch("/api/send-email-contact", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        setSuccessMessage("Your message has been sent successfully!");
        setName("");
        setEmail("");
        setSubject("");
        setMessage("");
      } else {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        const errorData = (await response.json()) as { error: string };
        setErrorMessage(
          `Failed to send message: ${errorData.error || "Unknown error"}`,
        );
      }
    } catch (error) {
      console.error("Error sending message:", error);
      setErrorMessage("Failed to send message. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mb-16 text-center">
        <h1 className="mb-4 text-4xl font-bold text-primary">Contact Us</h1>
        <p className="mx-auto max-w-2xl text-secondary">
          Have questions, feedback, or just want to say hello? We&#39;d love to
          hear from you.
        </p>
      </div>

      <div className="mb-16 grid grid-cols-1 gap-12 lg:grid-cols-2">
        <div>
          <form
            className="rounded-xl bg-secondary p-8 shadow-lg"
            onSubmit={handleSubmit}
          >
            <h2 className="mb-6 text-2xl font-bold text-primary">
              Send a Message
            </h2>

            {successMessage && (
              <div className="mb-4 rounded border border-green-500 bg-green-500/10 p-3 text-sm text-green-500">
                {successMessage}
              </div>
            )}

            {errorMessage && (
              <div className="mb-4 rounded border border-red-500 bg-red-500/10 p-3 text-sm text-red-500">
                {errorMessage}
              </div>
            )}

            <div className="mb-4">
              <label
                htmlFor="name"
                className="mb-1 block text-sm font-medium text-secondary"
              >
                Your Name
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>

            <div className="mb-4">
              <label
                htmlFor="email"
                className="mb-1 block text-sm font-medium text-secondary"
              >
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>

            <div className="mb-4">
              <label
                htmlFor="subject"
                className="mb-1 block text-sm font-medium text-secondary"
              >
                Subject
              </label>
              <select
                id="subject"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                required
              >
                <option value="">Select a subject</option>
                <option value="general">General Inquiry</option>
                <option value="support">Technical Support</option>
                <option value="partnership">Partnership Opportunity</option>
                <option value="feedback">Game Feedback</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="mb-6">
              <label
                htmlFor="message"
                className="mb-1 block text-sm font-medium text-secondary"
              >
                Your Message
              </label>
              <textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                rows={6}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                placeholder="Let us know how we can help..."
                required
              ></textarea>
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full rounded-lg bg-accent py-3 font-medium text-white transition hover:opacity-90 disabled:cursor-not-allowed disabled:opacity-70"
            >
              {loading ? "Sending..." : "Send Message"}
            </button>
          </form>
        </div>

        <div>
          <div className="mb-8 rounded-xl bg-secondary p-8 shadow-lg">
            <h2 className="mb-6 text-2xl font-bold text-primary">
              Contact Information
            </h2>

            <div className="space-y-6">
              <div className="flex items-start">
                <div className="bg-accent/20 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full text-accent">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-primary">Email</h3>
                  <a
                    href="mailto:<EMAIL>"
                    className="text-secondary hover:text-accent"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-accent/20 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full text-accent">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-primary">
                    Location
                  </h3>
                  <p className="text-secondary">
                    Someday soon we will have an office.
                  </p>
                </div>
              </div>

              <div className="flex items-start">
                <div className="bg-accent/20 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full text-accent">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold text-primary">Phone</h3>
                  <a
                    href="tel:+12065551234"
                    className="text-secondary hover:text-accent"
                  >
                    Placeholder Phone Number
                  </a>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-xl bg-secondary p-8 shadow-lg">
            <h2 className="mb-6 text-2xl font-bold text-primary">
              Connect With Us
            </h2>

            <div className="grid grid-cols-2 gap-4">
              {[
                {
                  name: "Twitter",
                  icon: "x-logo.png",
                  url: "https://twitter.com",
                },
                {
                  name: "Discord",
                  icon: "discord-logo.png",
                  url: "https://discord.gg",
                },
                {
                  name: "LinkedIn",
                  icon: "linkedin-logo.png",
                  url: "https://linkedin.com",
                },
                {
                  name: "GitHub",
                  icon: "github-logo.png",
                  url: "https://github.com",
                },
              ].map((social, i) => (
                <a
                  key={i}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="hover:bg-accent/10 flex items-center rounded-lg bg-primary p-4 transition"
                >
                  <div className="bg-accent/20 mr-3 flex h-8 w-8 items-center justify-center rounded-full">
                    <Image
                      src={`/images/socials/${social.icon}`}
                      alt={social.name}
                      width={30}
                      height={30}
                    />
                  </div>
                  <span className="text-primary">{social.name}</span>
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
