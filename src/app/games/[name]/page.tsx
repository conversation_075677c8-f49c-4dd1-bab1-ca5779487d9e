import { notFound } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { getGame, getPublicGamesIDs } from "@/lib/games";

export async function generateStaticParams() {
  console.log("generateStaticParams called for /games/[name]");
  try {
    const params = await getPublicGamesIDs();
    console.log("Game params generated:", params);
    return params;
  } catch (error) {
    console.error(
      "Error during generateStaticParams execution for games:",
      error,
    );
    // Return a placeholder to prevent build failure
    return [{ name: "placeholder" }];
  }
}

interface PageProps {
  params: {
    name: string;
  };
}

export default async function GamePage({ params }: PageProps) {
  const game = await getGame(params.name);

  if (!game) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mx-auto max-w-4xl">
        <Link
          href="/games"
          className="mb-8 inline-flex items-center text-secondary hover:text-accent"
        >
          ← Back to all games
        </Link>

        <h1 className="mb-6 text-4xl font-bold text-primary md:text-5xl">
          {game.title}
        </h1>

        {game.releaseDate && (
          <div className="mb-8 text-secondary">
            <span>
              Released:{" "}
              {game.releaseDate.toLocaleDateString(undefined, {
                year: "numeric",
                month: "long",
                day: "numeric",
              })}
            </span>
          </div>
        )}

        {game.coverImage && (
          <div className="mb-8 overflow-hidden rounded-xl">
            <Image
              src={game.coverImage}
              alt={game.title}
              width={1200}
              height={675}
              className="w-full object-cover"
            />
          </div>
        )}

        {game.description && (
          <div className="prose prose-lg prose-invert max-w-none">
            <p>{game.description}</p>
          </div>
        )}

        {/* Add more game details as needed */}
      </div>
    </div>
  );
}
