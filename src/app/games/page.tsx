import Link from "next/link";
import Image from "next/image";
import { getPublicGames } from "@/lib/games"; // Import from utility

export default async function GamesPage() {
  const games = await getPublicGames();

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mb-16 text-center">
        <h1 className="mb-4 text-4xl font-bold text-primary">Our Games</h1>
        <p className="mx-auto max-w-2xl text-secondary">
          Explore our collection of games and discover immersive experiences.
        </p>
      </div>

      <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
        {games?.length === 0 || games === null ? (
          <div className="col-span-full py-16 text-center text-secondary">
            <p className="mb-2 text-xl">No games available yet</p>
            <p>Check back soon for updates on our game releases.</p>
          </div>
        ) : (
          games?.map((game) => (
            <Link
              key={game.id}
              href={`/games/${game.name}`}
              className="transform overflow-hidden rounded-xl bg-secondary shadow-lg transition hover:-translate-y-1 hover:shadow-xl"
            >
              <div className="relative h-48 bg-gradient-to-r from-dark-orange to-bright-purple">
                {game.imageUrl ? (
                  <Image
                    src={game.imageUrl}
                    alt={game.title}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="code absolute inset-0 flex items-center justify-center text-center text-6xl text-white text-opacity-30">
                    {game.title}
                  </div>
                )}
              </div>
              <div className="p-6">
                <h2 className="mb-2 line-clamp-2 text-xl font-bold text-primary">
                  {game.title}
                </h2>
                <p className="mb-4 line-clamp-3 text-secondary">
                  {game.description}
                </p>
                <span className="text-accent hover:underline">
                  Learn more →
                </span>
              </div>
            </Link>
          ))
        )}
      </div>
    </div>
  );
}
