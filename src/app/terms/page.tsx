// src/app/terms/page.tsx
import { type Metadata } from "next";

export const metadata: Metadata = {
  title: "Terms of Service | Parallax Interactive",
  description: "Terms and conditions for using Parallax Interactive services",
};

export default function TermsOfServicePage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-6 text-4xl font-bold text-primary">
          Terms of Service
        </h1>
        <p className="mb-8 text-secondary">Last updated: April 15, 2025</p>

        <div className="prose prose-lg prose-invert max-w-none">
          <p>
            Welcome to Parallax Interactive! These Terms of Service
            (&#34;Terms&#34;) govern your use of our website, games, and related
            services (collectively, the &#34;Services&#34;). By accessing or
            using our Services, you agree to be bound by these Terms.
          </p>

          <h2>Acceptance of Terms</h2>
          <p>
            By using our Services, you agree to these Terms and our Privacy
            Policy. If you do not agree to these Terms, you may not use our
            Services.
          </p>

          <h2>Use of Services</h2>
          <p>
            You may use our Services only for lawful purposes and in accordance
            with these Terms. You agree not to:
          </p>
          <ul>
            <li>
              Use our Services in any way that violates any applicable law or
              regulation.
            </li>
            <li>
              Engage in any conduct that restricts or inhibits anyone&#39;s use
              or enjoyment of our Services.
            </li>
            <li>
              Use our Services to transmit, or procure the sending of, any
              advertising or promotional material without our prior written
              consent.
            </li>
            <li>
              Impersonate or attempt to impersonate Parallax Interactive, a
              Parallax Interactive employee, another user, or any other person
              or entity.
            </li>
            <li>
              Engage in any other conduct that, as determined by us, may harm
              Parallax Interactive or users of our Services.
            </li>
          </ul>

          <h2>Intellectual Property</h2>
          <p>
            The Services and their entire contents, features, and functionality
            (including but not limited to all information, software, text,
            displays, images, video, and audio, and the design, selection, and
            arrangement thereof) are owned by Parallax Interactive, its
            licensors, or other providers of such material and are protected by
            United States and international copyright, trademark, patent, trade
            secret, and other intellectual property or proprietary rights laws.
          </p>
          <p>
            These Terms permit you to use the Services for your personal,
            non-commercial use only. You must not reproduce, distribute, modify,
            create derivative works of, publicly display, publicly perform,
            republish, download, store, or transmit any of the material on our
            Services, except as follows:
          </p>
          <ul>
            <li>
              Your computer may temporarily store copies of such materials in
              RAM incidental to your accessing and viewing those materials.
            </li>
            <li>
              You may store files that are automatically cached by your Web
              browser for display enhancement purposes.
            </li>
            <li>
              You may print or download one copy of a reasonable number of pages
              of the Services for your own personal, non-commercial use and not
              for further reproduction, publication, or distribution.
            </li>
          </ul>

          <h2>User Content</h2>
          <p>
            Our Services may allow you to submit, upload, publish, or otherwise
            make available content, including but not limited to text, images,
            video, and audio (&#34;User Content&#34;). You retain all rights in,
            and are solely responsible for, the User Content you make available
            through the Services.
          </p>
          <p>
            By making any User Content available through the Services you grant
            to Parallax Interactive a non-exclusive, transferable,
            sub-licensable, royalty-free, worldwide license to use, copy,
            modify, create derivative works based on, distribute, publicly
            display, publicly perform, and otherwise exploit in any manner such
            User Content in all formats and distribution channels now known or
            hereafter devised (including in connection with the Services and
            Parallax Interactive&#39;s business and on third-party sites and
            services), without further notice to or consent from you, and
            without the requirement of payment to you or any other person or
            entity.
          </p>

          <h2>Disclaimer of Warranties</h2>
          <p>
            THE SERVICES ARE PROVIDED ON AN &#34;AS IS&#34; AND &#34;AS
            AVAILABLE&#34; BASIS, WITHOUT ANY WARRANTIES OF ANY KIND, EITHER
            EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF
            MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND
            NON-INFRINGEMENT. PARALLAX INTERACTIVE DOES NOT WARRANT THAT THE
            SERVICES WILL BE UNINTERRUPTED OR ERROR-FREE, THAT DEFECTS WILL BE
            CORRECTED, OR THAT THE SERVICES ARE FREE OF VIRUSES OR OTHER HARMFUL
            COMPONENTS.
          </p>

          <h2>Limitation of Liability</h2>
          <p>
            IN NO EVENT WILL PARALLAX INTERACTIVE, ITS AFFILIATES, OR THEIR
            LICENSORS, SERVICE PROVIDERS, EMPLOYEES, AGENTS, OFFICERS, OR
            DIRECTORS BE LIABLE FOR DAMAGES OF ANY KIND, UNDER ANY LEGAL THEORY,
            ARISING OUT OF OR IN CONNECTION WITH YOUR USE, OR INABILITY TO USE,
            THE SERVICES, INCLUDING ANY DIRECT, INDIRECT, SPECIAL, INCIDENTAL,
            CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING BUT NOT LIMITED TO,
            PERSONAL INJURY, PAIN AND SUFFERING, EMOTIONAL DISTRESS, LOSS OF
            REVENUE, LOSS OF PROFITS, LOSS OF BUSINESS OR ANTICIPATED SAVINGS,
            LOSS OF USE, LOSS OF GOODWILL, LOSS OF DATA, AND WHETHER CAUSED BY
            TORT (INCLUDING NEGLIGENCE), BREACH OF CONTRACT, OR OTHERWISE, EVEN
            IF FORESEEABLE.
          </p>

          <h2>Governing Law</h2>
          <p>
            These Terms and any dispute or claim arising out of, or related to,
            them, their subject matter or their formation (including
            non-contractual disputes or claims) shall be governed by and
            construed in accordance with the internal laws of the State of
            Washington without giving effect to any choice or conflict of law
            provision or rule.
          </p>

          <h2>Changes to Terms</h2>
          <p>
            We may revise and update these Terms from time to time in our sole
            discretion. All changes are effective immediately when we post them
            and apply to all access to and use of the Services thereafter.
          </p>
          <p>
            Your continued use of the Services following the posting of revised
            Terms means that you accept and agree to the changes. You are
            expected to check this page from time to time so you are aware of
            any changes, as they are binding on you.
          </p>

          <h2>Contact Us</h2>
          <p>If you have any questions about these Terms, please contact us:</p>
          <ul>
            <li>
              By visiting this page:{" "}
              <a
                href="https://docs.google.com/forms/d/e/1FAIpQLScVQm5GxwtF3Wnn3xyI1h4nUoQdskxr-enlA6ZZ7Fy0Mj-tEw/viewform"
                target="_blank"
              >
                Contact Us
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
