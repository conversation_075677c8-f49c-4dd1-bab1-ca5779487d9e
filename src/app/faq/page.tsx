"use client";

import { useState } from "react";

// FAQ component with accordion functionality
interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem = ({ question, answer }: FAQItemProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="border-b border-primary">
      <button
        className="flex w-full items-center justify-between px-4 py-5 text-left focus:outline-none"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-xl font-medium text-primary">{question}</span>
        <span
          className={`text-2xl text-accent transition-transform ${isOpen ? "rotate-180" : ""}`}
        >
          ↓
        </span>
      </button>
      <div
        className={`overflow-hidden transition-all ${isOpen ? "max-h-96 pb-5" : "max-h-0"}`}
      >
        <div
          className="prose prose-invert max-w-none px-4 text-secondary"
          dangerouslySetInnerHTML={{ __html: answer }}
        />
      </div>
    </div>
  );
};

export default function FAQPage() {
  const faqCategories = [
    {
      name: "General",
      questions: [
        {
          question: "What is Parallax Interactive?",
          answer:
            "Parallax Interactive is an independent game development studio founded in 2018. We focus on creating immersive gaming experiences with innovative mechanics and compelling narratives.",
        },
        {
          question: "Where are you located?",
          answer:
            "Our main studio is based in Seattle, Washington, but we have team members working remotely from different parts of the world.",
        },
        {
          question: "How can I contact your team?",
          answer:
            'You can reach out to us through our <a href="/contact" class="text-accent hover:underline">contact page</a>, or send an email <NAME_EMAIL>.',
        },
        {
          question: "Do you offer internships or job opportunities?",
          answer:
            'Yes! We periodically have openings for both internships and full-time positions. Check our <a href="/careers" class="text-accent hover:underline">careers page</a> for current opportunities.',
        },
      ],
    },
    {
      name: "Games & Products",
      questions: [
        {
          question: "What platforms do you develop for?",
          answer:
            "We primarily develop for PC (Steam, Epic Games Store), PlayStation, Xbox, and Nintendo Switch. We also have some mobile titles available on iOS and Android.",
        },
        {
          question: "Do you offer demo versions of your games?",
          answer:
            "Yes, we typically release demos for our major titles. These can be found on our respective store pages or occasionally during special events like Steam Game Festivals.",
        },
        {
          question: "How can I report a bug in one of your games?",
          answer:
            'You can report bugs through our <a href="/support" class="text-accent hover:underline">support portal</a> or by joining our Discord community where we have dedicated channels for bug reporting.',
        },
        {
          question: "Do you plan to localize your games into more languages?",
          answer:
            "We're constantly working to make our games available in more languages. Currently, our titles are available in English, Spanish, French, German, Japanese, and Chinese. We prioritize additional languages based on community demand.",
        },
      ],
    },
    {
      name: "Community & Support",
      questions: [
        {
          question: "How can I join your community?",
          answer:
            'The best way to join our community is through our <a href="https://discord.gg/parallaxinteractive" class="text-accent hover:underline">Discord server</a>, where you can chat with other players and our developers. You can also follow us on Twitter, Instagram, and other social media platforms.',
        },
        {
          question: "Do you accept game ideas or concepts from fans?",
          answer:
            "While we appreciate the creativity of our community, we generally don't accept unsolicited game ideas. This is primarily for legal reasons. However, we do listen closely to feedback about features and improvements for our existing games.",
        },
        {
          question: "How can I support your studio besides buying your games?",
          answer:
            'You can support us by joining our <a href="/donate" class="text-accent hover:underline">membership program</a>, spreading the word about our games, participating in our community events, or contributing to our open-source projects on GitHub.',
        },
        {
          question: "Do you participate in game jams or charity events?",
          answer:
            "Yes! We regularly participate in game jams like Ludum Dare and Global Game Jam. We also organize charity streams and events throughout the year, with proceeds going to various non-profit organizations.",
        },
      ],
    },
    {
      name: "Technical",
      questions: [
        {
          question: "What are the minimum system requirements for your games?",
          answer:
            "System requirements vary by game. Please check the specific game page or store listing for detailed requirements. In general, our games are designed to run well on a wide range of hardware.",
        },
        {
          question: "Do your games support controllers?",
          answer:
            "Yes, all our PC games support standard controllers including Xbox, PlayStation, and Nintendo Switch Pro controllers. Most games also feature customizable control schemes.",
        },
        {
          question: "Are your games available in offline mode?",
          answer:
            "Most of our single-player games can be played completely offline after the initial activation. Some features like leaderboards or community challenges may require an internet connection.",
        },
        {
          question: "Do you plan to support VR or AR technologies?",
          answer:
            "We're actively exploring VR and AR technologies and have prototypes in development. Stay tuned to our announcements for more details on upcoming VR/AR projects.",
        },
      ],
    },
  ];

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="mb-12 text-center">
        <h1 className="mb-4 text-4xl font-bold text-primary">
          Frequently Asked Questions
        </h1>
        <p className="mx-auto max-w-2xl text-secondary">
          Find answers to commonly asked questions about our games, studio, and
          community.
        </p>
      </div>

      <div className="mx-auto max-w-4xl">
        {faqCategories.map((category, i) => (
          <div key={i} className="mb-12">
            <h2 className="mb-6 border-b border-accent pb-2 text-2xl font-bold text-primary">
              {category.name}
            </h2>

            <div className="overflow-hidden rounded-xl bg-secondary">
              {category.questions.map((faq, j) => (
                <FAQItem key={j} question={faq.question} answer={faq.answer} />
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="mx-auto mt-16 max-w-4xl rounded-xl bg-secondary p-8 shadow-lg">
        <h2 className="mb-4 text-2xl font-bold text-primary">
          Still have questions?
        </h2>
        <p className="mb-6 text-secondary">
          If you couldn&#39;t find the answer you were looking for, please reach
          out to our team.
        </p>
        <div className="flex flex-wrap gap-4">
          <a
            href="/contact"
            className="rounded-lg bg-accent px-6 py-3 font-medium text-white transition hover:opacity-90"
          >
            Contact Us
          </a>
          <a
            href="/support"
            className="rounded-lg border border-accent bg-transparent px-6 py-3 font-medium text-accent transition hover:bg-accent hover:text-white"
          >
            Visit Support Center
          </a>
        </div>
      </div>
    </div>
  );
}
