import Image from "next/image";
import { employees } from "@/lib/employees";
import { SvgFileColorChanger } from "@/lib/svgColorChanger";
import { ParallaxBackground } from "@/lib/useParallax";

export default function AboutPage() {
  return (
    <div>
      <ParallaxBackground
        src="/images/placeholder.png"
        alt="The Parallax Interactive team"
        speed={0.5}
        containerStyle={{
          height: "100vh", // Full viewport height
          position: "absolute", // Position behind content
          zIndex: -1, // Put behind other content
        }}
      />
      <div className="container mx-auto px-4 py-10">
        <div className="pb-5">
          <div className="shadow-accent mb-12 rounded-xl bg-secondary p-8 text-center shadow-lg">
            <h1 className="mb-2 text-5xl font-bold text-accent">
              About Parallax Interactive
            </h1>
            <p className="mx-auto max-w-3xl text-secondary">
              We&#39;re a passionate team of developers, artists, and designers
              dedicated to creating immersive gaming experiences that push the
              boundaries of imagination.
            </p>
          </div>
          <div className="mb-12 rounded-xl bg-secondary p-8 shadow-lg">
            <h2 className="mb-4 text-center text-2xl font-bold text-primary">
              Our Story
            </h2>
            <p className="mb-4 text-secondary">
              We were originally founded in October 2022 with the idea of
              Subject 9. Subject 9 was to be a small, open-world game taking
              place in an abandoned nuclear power facility. The young
              protagonist was to be confused about why they were there, meeting
              many horrifying monstrosities in his attempt to escape. The game
              was originally to be created by a sole developer, who later
              founded Parallax Interactive.
            </p>
            <p className="mb-4 text-secondary">
              Through the coming months, the storyline of Subject 9 became
              deeper and more complex. Leading into the new year of 2023, the
              story grew past the ability of one developer. Here, the first
              members of the team were hired. From the beginning months of 2023
              to the next fall, the project expanded more and more. Finally, in
              September of 2023, real production began with the 3D modelers. The
              storyline for the game was well developed by this point, and
              extremely unique.
            </p>
            <p className="mb-4 text-secondary">
              January of 2024 marked the beginning of the development portion.
              By this point, Flydog Games, the former name of Parallax
              Interactive, had well over a dozen team members, some of which
              took on multiple positions. Throughout this year, an obvious
              problem became apparent: Subject 9 became too large to handle for
              such a new team. In the fall of 2024, two major events happened.
              First, the complete redesign of Flydog Games, moving into a more
              mature environment under Parallax interactive. Secondly, the
              project of Subject 9 was postponed, and our new priority shifted
              to building an awesome community with real-quality games behind
              our name.
            </p>
            <p className="text-secondary">
              Our current position is described in four places: Team
              development, community growth, founding our LLC, and building a
              reputable name for said LLC. Team development has always been an
              issue for us, so creating smaller successful games can draw more
              attention from experienced developers. Community growth is crucial
              in producing such a marketable and story-rich game. We see
              founding our LLC as necessary for tackling big projects like the
              SUBJECT series. Lastly, building a reputable name is the most
              important step as it captivates all the previously mentioned
              goals.
            </p>
          </div>
        </div>

        <div className="mb-16 rounded-xl bg-secondary p-8 shadow-lg">
          <h2 className="mb-8 text-center text-2xl font-bold text-primary">
            Our Core Values
          </h2>
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {[
              {
                title: "Creative Innovation",
                description:
                  "We pursue new ideas and mechanics that challenge conventional game design.",
                icon: "💡",
              },
              {
                title: "Player-Centered",
                description:
                  "Our community's feedback shapes our development process and priorities.",
                icon: "🎮",
              },
              {
                title: "Narrative Excellence",
                description:
                  "We believe in the power of stories to create meaningful gaming experiences.",
                icon: "📚",
              },
              {
                title: "Transparency",
                description:
                  "We maintain open communication with our community about our development process.",
                icon: "🔍",
              },
            ].map((value, i) => (
              <div key={i} className="text-center">
                <div className="mb-4 text-4xl">{value.icon}</div>
                <h3 className="mb-2 text-xl font-semibold text-primary">
                  {value.title}
                </h3>
                <p className="text-secondary">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        <h2 className="mb-8 text-center text-2xl font-bold text-primary">
          Meet Our Team
        </h2>
        <div className="mb-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {employees.map((member, i) => (
            <div
              key={i}
              className="overflow-hidden rounded-xl bg-secondary shadow-lg"
            >
              <div className="relative h-64">
                <Image
                  src={`/images/employees/${member.image}`}
                  alt={member.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 z-10 flex items-end justify-start text-center text-3xl text-accent text-opacity-30">
                  <span className="rounded-r-lg bg-primary p-2">
                    {member.name}
                  </span>
                </div>
                <div className="absolute inset-0 z-10 flex items-end justify-end text-3xl text-accent text-opacity-30">
                  <span className="rounded-l-lg bg-primary p-2">
                    {member.socials.map((social, i) => (
                      <a
                        href={social.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        key={i}
                        title={`${member.name}'s ${social.name} profile`}
                      >
                        <div className="transition-transform hover:scale-125">
                          <SvgFileColorChanger
                            src={`/images/socials/${social.name.toLowerCase() + "-logo.svg"}`}
                            width={25}
                            height={25}
                            colorMap={{}}
                          />
                        </div>
                      </a>
                    ))}
                  </span>
                </div>
              </div>
              <div className="p-6">
                <p className="mb-3 text-accent">{member.role}</p>
                <p className="text-secondary">{member.bio}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="border-border-primary rounded-xl border bg-primary p-8 shadow-lg">
          <h2 className="mb-6 text-center text-2xl font-bold text-primary">
            Join Our Journey
          </h2>
          <p className="mx-auto mb-8 max-w-3xl text-center text-secondary">
            We&#39;re always looking for passionate individuals to join our
            team. Whether you&#39;re a developer, artist, designer, or writer,
            if you&#39;re excited about creating innovative games, we&#39;d love
            to hear from you.
          </p>
          <div className="flex justify-center">
            <a
              href="/join-us"
              className="rounded-lg bg-accent px-8 py-3 font-medium text-white transition hover:opacity-90"
            >
              View Open Positions
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
