"use client";

import ProtectedRoute from "@/components/auth/protected-route";
import { useState, useMemo } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";

//
// Mock Markdown Content
//

const styleGuideContent = `
# Frontend Style Guide

This document outlines the styling and component standards for our project.

## Colors

Our color palette is defined in \`tailwind.config.js\` using CSS variables.

- **Primary Text**: \`var(--text-primary)\`
- **Secondary Text**: \`var(--text-secondary)\`
- **Primary Background**: \`var(--bg-primary)\`
- **Accent Color**: \`var(--bg-accent)\`

Always use the semantic names (\`text-primary\`, \`bg-secondary\`, etc.) instead of hardcoded colors like \`text-gray-900\`.

---

## Component Structure

React components should be structured logically.

\`\`\`tsx
"use client";

import React from 'react';

// 1. Type/Interface definitions
interface MyComponentProps {
  title: string;
}

// 2. Component definition
const MyComponent: React.FC<MyComponentProps> = ({ title }) => {
  // 3. State and Hooks
  const [count, setCount] = useState(0);

  // 4. Render logic
  return (
    <div>
      <h1>{title}</h1>
      <p>Count: {count}</p>
      <button onClick={() => setCount(c => c + 1)}>
        Increment
      </button>
    </div>
  );
};

export default MyComponent;
\`\`\`

Adhering to this structure improves readability and maintainability.

---

## Typography

The primary font is Geist Sans, configured in \`tailwind.config.js\`.

- **Headings**: Use \`h1\`, \`h2\`, etc. with Tailwind's font-size utilities (\`text-3xl\`, \`text-2xl\`).
- **Body Text**: Standard paragraph tags will use the default sans-serif font size.
- **Code**: Use backticks for inline code and triple-backticks for code blocks.
`;

const sopContent = `
# Standard Operating Procedures (SOPs)

This document details standard procedures for development workflows.

## Git Branching Strategy

We use a simplified GitFlow model.

1.  **main**: This branch is always production-ready. Do not commit directly to \`main\`.
2.  **develop**: This is the primary development branch. All feature branches are merged into \`develop\`.
3.  **feature/your-feature-name**: Create a new branch from \`develop\` for every new feature or task.

Pull requests (PRs) are required to merge into \`develop\`.

---

## Code Commits

Commit messages should be clear and concise, following the Conventional Commits specification.

**Format**: \`<type>[optional scope]: <description>\`

**Example**:
\`\`\`
feat(kanban): add drag and drop for tasks
fix(auth): resolve issue with token expiration
docs(readme): update setup instructions
\`\`\`

This helps in auto-generating changelogs and understanding project history.

---

## Task Management

All work must be tracked in the Kanban board.

- **New Tasks**: Create a new task card in the 'To Do' column.
- **Assigning**: Assign the task to yourself when you begin work.
- **Progress**: Move the card across the board as you complete the work (\`In Progress\` -> \`In Review\` -> \`Done\`).
- **Details**: Ensure tasks have a clear description, priority, and any relevant links.
`;

const documents = {
  style: {
    title: "Style Guide",
    content: styleGuideContent,
  },
  sop: {
    title: "SOPs",
    content: sopContent,
  },
};

type DocumentKey = keyof typeof documents;

//
// Main Component
//

export default function DocumentationPage() {
  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-primary">Documentation</h1>
          <p className="mt-1 text-secondary">
            Style guides, standard operating procedures, and more.
          </p>
        </div>
        <DocumentationViewer />
      </div>
    </ProtectedRoute>
  );
}

function DocumentationViewer() {
  const [activeDoc, setActiveDoc] = useState<DocumentKey>("style");
  const [currentPage, setCurrentPage] = useState(0);

  const pages = useMemo(() => {
    const content = documents[activeDoc].content;
    return content.split("---").map((page) => page.trim());
  }, [activeDoc]);

  const totalPages = pages.length;

  const goToNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages - 1));
  };

  const goToPrevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 0));
  };

  return (
    <div className="rounded-lg bg-secondary">
      <div className="border-b border-primary">
        <nav className="-mb-px flex space-x-6 px-6" aria-label="Tabs">
          {Object.entries(documents).map(([key, doc]) => (
            <button
              key={key}
              onClick={() => {
                setActiveDoc(key as DocumentKey);
                setCurrentPage(0);
              }}
              className={`${
                activeDoc === key
                  ? "border-accent text-accent"
                  : "border-transparent text-secondary hover:border-gray-300 hover:text-primary"
              } whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium transition`}
            >
              {doc.title}
            </button>
          ))}
        </nav>
      </div>

      <div className="prose prose-invert max-w-none p-6 text-primary prose-headings:text-primary prose-strong:text-primary prose-a:text-accent prose-code:rounded prose-code:bg-code-block prose-code:p-1 prose-code:text-light-tan prose-pre:bg-code-block md:p-8">
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {pages[currentPage] ?? ""}
        </ReactMarkdown>
      </div>

      {totalPages > 1 && (
        <div className="flex items-center justify-between border-t border-primary px-6 py-4">
          <button
            onClick={goToPrevPage}
            disabled={currentPage === 0}
            aria-label="Go to previous page"
            className="rounded-md border border-primary px-4 py-2 text-sm font-medium text-primary transition hover:bg-primary/10 disabled:opacity-50"
          >
            Previous
          </button>
          <span className="text-sm text-secondary">
            Page {currentPage + 1} of {totalPages}
          </span>
          <button
            onClick={goToNextPage}
            disabled={currentPage === totalPages - 1}
            aria-label="Go to next page"
            className="rounded-md border border-primary px-4 py-2 text-sm font-medium text-primary transition hover:bg-primary/10 disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  );
}