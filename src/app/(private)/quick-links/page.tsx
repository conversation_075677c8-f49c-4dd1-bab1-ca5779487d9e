"use client";

import ProtectedRoute from "@/components/auth/protected-route";
import {
  BookOpen,
  Briefcase,
  Code,
  Database,
  GitBranch,
  Palette,
} from "lucide-react";
import Link from "next/link";

//
// Mock Data
//

const links = [
  {
    category: "Development",
    items: [
      {
        title: "Game Design Document (GDD)",
        description: "The central design document for the project.",
        href: "#",
        icon: <BookOpen />,
      },
      {
        title: "Main Git Repository",
        description: "Source code for the primary game project.",
        href: "#",
        icon: <GitBranch />,
      },
      {
        title: "CI/CD Pipeline",
        description: "Manage and monitor automated builds and deployments.",
        href: "#",
        icon: <Code />,
      },
    ],
  },
  {
    category: "Art & Assets",
    items: [
      {
        title: "Art Asset Library",
        description: "Dropbox/Drive folder for all 2D/3D assets.",
        href: "#",
        icon: <Palette />,
      },
      {
        title: "Sound & Music Library",
        description: "Central repository for all audio files.",
        href: "#",
        icon: <Database />,
      },
    ],
  },
  {
    category: "Company",
    items: [
      {
        title: "Company Wiki",
        description: "Internal knowledge base and company policies.",
        href: "#",
        icon: <Briefcase />,
      },
    ],
  },
];

//
// Main Component
//

export default function QuickLinksPage() {
  return (
    <ProtectedRoute>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-primary">Quick Links</h1>
          <p className="mt-1 text-secondary">
            Your central hub for important tools and resources.
          </p>
        </div>

        <div className="space-y-10">
          {links.map((category) => (
            <div key={category.category}>
              <h2 className="mb-4 text-xl font-semibold text-primary">
                {category.category}
              </h2>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
                {category.items.map((link) => (
                  <Link
                    key={link.title}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="group block rounded-lg bg-secondary p-6 transition-all hover:-translate-y-1 hover:shadow-xl"
                  >
                    <div className="mb-4 text-accent">{link.icon}</div>
                    <h3 className="mb-2 font-semibold text-primary">
                      {link.title}
                    </h3>
                    <p className="text-sm text-secondary">
                      {link.description}
                    </p>
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </ProtectedRoute>
  );
}