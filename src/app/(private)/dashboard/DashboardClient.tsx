"use client";

import Link from "next/link";
import { useAuth } from "@/lib/auth-context";
import { useCallback, useEffect, useState } from "react";
import { collection, getDocs, limit, orderBy, query } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { hasRole } from "@/lib/utils";
import { Activity, CheckCircle, Flag, XCircle } from "lucide-react";
import type { KanbanTask } from "@/types/kanban";

//
// Types
//

interface AnnouncementData {
  content: string;
  createdAt: unknown;
  updatedAt?: unknown;
}

interface GitHubActivity {
  user: string;
  action: string;
  target: string;
  time: string;
  url: string;
}

interface DashboardPageProps {
  activity: GitHubActivity[];
  activityError: string | null;
  tasks?: KanbanTask[] | null | undefined;
  builds?: BuildData[] | null | undefined;
  milestones?: MilestoneData[] | null | undefined;
}

//
// Mock Data (for client-side components)
//

const builds = [
  { name: "Dev Branch", status: "Success", version: "0.8.1523" },
  { name: "QA Branch", status: "Success", version: "0.8.1519" },
  { name: "Nightly Build", status: "Failed", version: "0.8.1511" },
];

const milestones = [
  { name: "Vertical Slice", date: "2025-05-20", completed: true },
  { name: "Alpha Release", date: "2025-07-15", completed: false },
  { name: "Content Lock", date: "2025-09-01", completed: false },
  { name: "Beta Release", date: "2025-10-10", completed: false },
];

//
// Main Client Component
//

export default function DashboardClient({
  activity,
  activityError,
}: DashboardPageProps) {
  const { user, signOut } = useAuth();
  const isAuthenticated = !!user;
  const [announcement, setAnnouncement] = useState("");
  const [editingAnnouncement, setEditingAnnouncement] = useState(false);
  const [tasks, setTasks] = useState<KanbanTask[]>([]);

  const fetchAnnouncement = useCallback(async () => {
    try {
      if (!db) return;
      const q = query(
        collection(db, "announcements"),
        orderBy("createdAt", "desc"),
        limit(1),
      );
      const snapshot = await getDocs(q);
      if (!snapshot.empty) {
        const data = snapshot.docs[0]?.data() as AnnouncementData;
        setAnnouncement(data.content ?? "");
      } else {
        setAnnouncement("");
      }
    } catch (error) {
      console.error("Error fetching announcement:", error);
      setAnnouncement("");
    }
  }, []);

  const fetchTasks = useCallback(async () => {
    try {
      if (!db) return;
      const tasksSnapshot = await getDocs(collection(db, "kanban_tasks"));
      const fetchedTasks = tasksSnapshot.docs.map(
        (doc) => doc.data() as KanbanTask,
      );
      setTasks(fetchedTasks);
    } catch (error) {
      console.error("Error fetching tasks:", error);
      setTasks([]);
    }
  }, []);

  useEffect(() => {
    void fetchAnnouncement();
    if (isAuthenticated) {
      fetchAnnouncement()
        .then(() => {
          // Consider using a logger or removing in production
          console.log("Fetching Announcement");
        })
        .catch((error) => {
          console.error("Error in fetching announcement:", error);
        });
      fetchTasks()
        .then(() => {
          // Consider using a logger or removing in production
          console.log("Fetching Tasks");
        })
        .catch((error) => {
          console.error("Error in fetching tasks:", error);
        });
    }
  }, [fetchAnnouncement, fetchTasks, isAuthenticated]);

  const handleSaveAnnouncement = async () => {
    try {
      if (!db || !announcement.trim()) return;
      const q = query(
        collection(db, "announcements"),
        orderBy("createdAt", "desc"),
        limit(1),
      );
      const snapshot = await getDocs(q);
      if (!snapshot.empty) {
        const data = snapshot?.docs[0]?.data() as AnnouncementData;
        setAnnouncement(data.content ?? "");
      } else {
        setAnnouncement(""); // Clear announcement if none exists
      }

      setEditingAnnouncement(false);
      await fetchAnnouncement();
    } catch (error) {
      console.error("Error saving announcement:", error);
      setAnnouncement("");
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-primary">
            Developer Dashboard
          </h1>
          <p className="mt-1 text-secondary">Welcome back, {user?.email}</p>
        </div>
        <button
          onClick={() => signOut()}
          className="border-border-primary hover:bg-primary/10 mt-4 rounded-lg border bg-transparent px-4 py-2 text-primary transition md:mt-0"
        >
          Sign Out
        </button>
      </div>

      <AnnouncementCard
        announcement={announcement}
        setAnnouncement={setAnnouncement}
        editing={editingAnnouncement}
        setEditing={setEditingAnnouncement}
        onSave={handleSaveAnnouncement}
        canEdit={hasRole(user, "admin") || hasRole(user, "writer")}
      />

      <ProductivityTabs
        activity={activity}
        activityError={activityError}
        tasks={tasks}
        builds={builds}
        milestones={milestones}
      />

      <div className="mt-12">
        <h2 className="mb-4 text-xl font-semibold text-primary">
          Tools & Resources
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {user && hasRole(user, "admin") && (
            <DashboardCard
              title="Manage Users"
              description="Add, edit, and remove team members and their roles."
              icon="👥"
              href="/user-management"
            />
          )}
          {user && (hasRole(user, "admin") || hasRole(user, "writer")) && (
            <DashboardCard
              title="Manage Roadmap"
              description="Update and manage the public roadmap for ongoing projects."
              icon="🗺️"
              href="/roadmap-management"
            />
          )}
          {user && (hasRole(user, "admin") || hasRole(user, "writer")) && (
            <DashboardCard
              title="Manage Game Posts"
              description="Create, edit, and publish game posts for the community."
              icon="🎮"
              href="/game-management"
            />
          )}
          {user && (hasRole(user, "admin") || hasRole(user, "writer")) && (
            <DashboardCard
              title="Manage Blog Posts"
              description="Create, edit, and publish blog posts for the community."
              icon="📝"
              href="/internal-blog"
            />
          )}
          <DashboardCard
            title="Spreadsheets"
            description="Access and manage project spreadsheets and data."
            icon="📊"
            href="/spreadsheets"
          />
          <DashboardCard
            title="Team Blog"
            description="Read internal team blog posts."
            icon="📖"
            href="/team-blog"
          />
          <DashboardCard
            title="Task Board"
            description="Manage tasks and projects."
            icon="📋"
            href="/kanban"
          />
          <DashboardCard
            title="Quick Links"
            description="Access essential documents and tools."
            icon="🔗"
            href="/quick-links"
          />
          <DashboardCard
            title="Documentation"
            description="View style guides and SOPs."
            icon="📚"
            href="/documentation"
          />
        </div>
      </div>
    </div>
  );
}

//
// Sub-Components
//

function AnnouncementCard({
  announcement,
  setAnnouncement,
  editing,
  setEditing,
  onSave,
  canEdit,
}: {
  announcement: string;
  setAnnouncement: (val: string) => void;
  editing: boolean;
  setEditing: (val: boolean) => void;
  onSave: () => void;
  canEdit: boolean;
}) {
  return (
    <div className="mb-8 rounded-lg bg-secondary p-6">
      <h2 className="mb-4 text-xl font-semibold text-primary">
        Team Announcement
      </h2>
      {canEdit ? (
        <>
          {editing ? (
            <>
              <textarea
                aria-label="Announcement"
                value={announcement}
                onChange={(e) => setAnnouncement(e.target.value)}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                rows={4}
              />
              <div className="mt-4 flex justify-end gap-2">
                <button
                  onClick={() => setEditing(false)}
                  className="border-border-primary hover:bg-primary/10 rounded-lg border px-4 py-2 text-primary transition"
                >
                  Cancel
                </button>
                <button
                  onClick={onSave}
                  className="rounded-lg bg-accent px-4 py-2 text-white transition hover:opacity-90"
                >
                  Save
                </button>
              </div>
            </>
          ) : (
            <>
              <p className="text-secondary">
                {announcement || "No announcement yet."}
              </p>
              <button
                onClick={() => setEditing(true)}
                className="border-border-primary hover:bg-primary/10 mt-4 rounded-lg border px-4 py-2 text-primary transition"
              >
                Edit Announcement
              </button>
            </>
          )}
        </>
      ) : (
        <p className="text-secondary">
          {announcement || "No announcement yet."}
        </p>
      )}
    </div>
  );
}

interface BuildData {
  name: string;
  status: string;
  version: string;
}

interface MilestoneData {
  name: string;
  date: string;
  completed: boolean;
}

interface ProductivityTabsProps {
  activity: GitHubActivity[];
  activityError: string | null;
  tasks: KanbanTask[] | null | undefined;
  // Add these when updated
  builds?: BuildData[];
  milestones?: MilestoneData[];
}

function ProductivityTabs({
  activity,
  activityError,
  tasks,
  builds,
  milestones,
}: ProductivityTabsProps) {
  const [activeTab, setActiveTab] = useState("activity");

  const tabs = [
    { id: "activity", label: "Recent Activity", hasData: true },
    { id: "tasks", label: "My Tasks", hasData: !!tasks },
    { id: "builds", label: "Build Status", hasData: !!builds },
    { id: "milestones", label: "Milestones", hasData: !!milestones },
  ];

  return (
    <div className="rounded-lg bg-secondary p-6">
      <div className="border-b border-primary">
        <nav className="-mb-px flex space-x-6" role="tablist" aria-label="Tabs">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              role="tab"
              aria-selected={activeTab === tab.id}
              aria-controls={`panel-${tab.id}`}
              tabIndex={activeTab === tab.id ? 0 : -1}
              disabled={!tab.hasData}
              className={`${
                activeTab === tab.id
                  ? "border-accent text-accent"
                  : "text-muted-foreground border-transparent hover:border-gray-300 hover:text-primary"
              } whitespace-nowrap border-b-2 px-1 py-4 text-sm font-medium transition disabled:cursor-not-allowed disabled:opacity-50`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <div className="pt-6">
        {activeTab === "activity" && (
          <div
            id="panel-activity"
            role="tabpanel"
            aria-labelledby="tab-activity"
          >
            <ActivityFeed activity={activity} error={activityError} />
          </div>
        )}
        {activeTab === "tasks" && tasks && (
          <div id="panel-tasks" role="tabpanel" aria-labelledby="tab-tasks">
            <MyOpenTasks tasks={tasks} />
          </div>
        )}
        {activeTab === "builds" && builds && (
          <div id="panel-builds" role="tabpanel" aria-labelledby="tab-builds">
            <BuildStatus builds={builds} />
          </div>
        )}
        {activeTab === "milestones" && milestones && (
          <div
            id="panel-milestones"
            role="tabpanel"
            aria-labelledby="tab-milestones"
          >
            <MilestoneTracker milestones={milestones} />
          </div>
        )}
      </div>
    </div>
  );
}

function ActivityFeed({
  activity,
  error,
}: {
  activity: GitHubActivity[];
  error: string | null;
}) {
  if (error) {
    return <p className="text-red-500">{error}</p>;
  }

  if (!activity || activity.length === 0) {
    return <p className="text-secondary">No recent activity found.</p>;
  }

  return (
    <ul className="space-y-4">
      {activity.map((item, index) => (
        <li key={index} className="flex items-center text-sm">
          <Activity size={16} className="mr-3 text-secondary" />
          <Link
            href={item.url}
            target="_blank"
            rel="noopener noreferrer"
            className="group flex w-full items-center"
          >
            <span className="mr-2 font-semibold text-primary">{item.user}</span>
            <span className="text-secondary">{item.action}</span>
            <span className="mx-2 font-medium text-accent group-hover:underline">
              {item.target}
            </span>
            <span className="ml-auto text-xs text-secondary">{item.time}</span>
          </Link>
        </li>
      ))}
    </ul>
  );
}

function MyOpenTasks({ tasks }: { tasks: KanbanTask[] }) {
  const priorityConfig = {
    low: { color: "text-green-600 dark:text-green-400" },
    medium: { color: "text-yellow-600 dark:text-yellow-400" },
    high: { color: "text-orange-600 dark:text-orange-400" },
    critical: { color: "text-red-600 dark:text-red-400" },
  };
  return (
    <ul className="space-y-3">
      {tasks.map((task) => (
        <li key={task.id} className="text-primary">
          <Link
            href={`/kanban?task=${task.id}`}
            className="hover:bg-primary/10 group flex items-center rounded-md p-2 transition"
          >
            <span className="font-mono text-xs text-accent">task id: {task.id}</span>
            <span className="ml-3 group-hover:underline">{task.title}</span>
            {task.description && (
              <span className="ml-2 text-xs text-secondary">
                {task.description}
              </span>
            )}
            {task.dueDate && (
              <span className="ml-auto text-xs text-secondary">
                {task.dueDate.toDate().toLocaleDateString()}
              </span>
            )}
            {task.priority && (
              <span className={`ml-2 text-xs text-secondary ${priorityConfig[task.priority].color}`}>
                {task.priority}
              </span>
            )}
          </Link>
        </li>
      ))}
    </ul>
  );
}

interface BuildStatusProps {
  builds?: BuildData[];
}

function BuildStatus({ builds }: BuildStatusProps) {
  return (
    <div className="space-y-3">
      {builds?.map((build) => (
        <div key={build.name} className="flex items-center justify-between">
          <div className="flex items-center">
            {build.status === "Success" ? (
              <CheckCircle size={18} className="mr-3 text-green-500" />
            ) : (
              <XCircle size={18} className="mr-3 text-red-500" />
            )}
            <span className="text-primary">{build.name}</span>
          </div>
          <span className="font-mono text-sm text-secondary">
            {build.version}
          </span>
        </div>
      ))}
    </div>
  );
}

interface MilestoneTrackerProps {
  milestones?: MilestoneData[];
}

function MilestoneTracker({ milestones }: MilestoneTrackerProps) {
  return (
    <div className="space-y-3">
      {milestones?.map((milestone) => (
        <div
          key={milestone.name}
          className={`flex items-center ${
            milestone.completed ? "text-secondary line-through" : "text-primary"
          }`}
        >
          <Flag
            size={16}
            className={`mr-3 ${
              milestone.completed ? "text-green-500" : "text-accent"
            }`}
          />
          <div className="flex w-full justify-between">
            <span>{milestone.name}</span>
            <span className="text-sm">{milestone.date}</span>
          </div>
        </div>
      ))}
    </div>
  );
}

function DashboardCard({
  title,
  description,
  icon,
  href,
}: {
  title: string;
  description: string;
  icon: string;
  href: string;
}) {
  return (
    <Link
      href={href}
      className="block rounded-lg bg-secondary p-6 transition-shadow hover:shadow-lg"
    >
      <div className="mb-4 text-4xl">{icon}</div>
      <h3 className="mb-2 text-xl font-semibold text-primary">{title}</h3>
      <p className="text-secondary">{description}</p>
    </Link>
  );
}
