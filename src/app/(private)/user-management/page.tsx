"use client";

import { useCallback, useEffect, useState } from "react";
import {
  collection,
  doc,
  getDoc,
  getDocs,
  updateDoc,
} from "firebase/firestore";
import { db } from "@/lib/firebase";

// Modified interface to make roles optional
interface User {
  uid: string;
  email: string | null;
  roles?: string[]; // Mark as optional
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const usersSnapshot = await getDocs(collection(db, "users"));

      // Transform and validate the data more carefully
      const usersList = usersSnapshot.docs.map((doc) => {
        const data = doc.data();
        // Console log to debug
        console.log(`User ${doc.id} data:`, data);

        // Ensure we have a properly formatted user object
        return {
          uid: doc.id,
          email: typeof data.email === "string" ? data.email : null,
          // Explicitly ensure roles is an array
          roles: Array.isArray(data.roles)
            ? data.roles.filter(
                (role): role is string => typeof role === "string",
              )
            : [],
        };
      });

      setUsers(usersList);
    } catch (error) {
      console.error("Error fetching users:", error);
      setError("Failed to load users. Please try again later.");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    void fetchUsers();
  }, [fetchUsers]);

  const handleRoleChange = async (
    uid: string,
    role: string,
    checked: boolean,
  ) => {
    try {
      const userDocRef = doc(db, "users", uid);
      const userDoc = await getDoc(userDocRef);
      if (userDoc.exists()) {
        const userData = userDoc.data();
        // Ensure we're working with an array
        const currentRoles = Array.isArray(userData.roles)
          ? userData.roles.filter(
              (role): role is string => typeof role === "string",
            )
          : [];

        let updatedRoles: string[];
        if (checked && !currentRoles.includes(role)) {
          updatedRoles = [...currentRoles, role];
        } else if (!checked) {
          updatedRoles = currentRoles.filter((r) => r !== role);
        } else {
          // No change needed
          updatedRoles = currentRoles;
        }

        await updateDoc(userDocRef, { roles: updatedRoles });
        await fetchUsers(); // Refresh user list
      } else {
        console.error("User document not found for UID:", uid);
      }
    } catch (error) {
      console.error("Error updating user roles:", error);
    }
  };

  // Function to safely check if a user has a role
  const hasRole = (user: User, role: string): boolean => {
    // Multiple layers of protection
    if (!user) return false;
    if (!user.roles) return false;
    if (!Array.isArray(user.roles)) return false;
    return user.roles.includes(role);
  };

  return (
    <div className="rounded-lg bg-secondary p-6">
      <h2 className="mb-4 text-xl font-semibold text-primary">Manage Users</h2>

      {error && (
        <div className="mb-4 rounded-md bg-red-500/20 p-4 text-red-500">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center py-8">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-accent"></div>
        </div>
      ) : users.length === 0 ? (
        <p className="text-secondary">No users found.</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-primary">
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-secondary">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-secondary">
                  Roles
                </th>
              </tr>
            </thead>
            <tbody className="divide-border-primary divide-y">
              {users.map((user) => (
                <tr key={user.uid} className="hover:bg-primary/50">
                  <td className="whitespace-nowrap px-6 py-4 text-primary">
                    {user.email ?? "No email"}
                  </td>
                  <td className="whitespace-nowrap px-6 py-4 text-secondary">
                    <label className="mr-4 inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-5 w-5 text-accent"
                        checked={hasRole(user, "admin")}
                        onChange={(e) =>
                          handleRoleChange(user.uid, "admin", e.target.checked)
                        }
                      />
                      <span className="ml-2 text-secondary">Admin</span>
                    </label>
                    <label className="mr-4 inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-5 w-5 text-accent"
                        checked={hasRole(user, "teamLead")}
                        onChange={(e) =>
                          handleRoleChange(
                            user.uid,
                            "teamLead",
                            e.target.checked,
                          )
                        }
                      />
                      <span className="ml-2 text-secondary">Team Lead</span>
                    </label>
                    <label className="mr-4 inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-5 w-5 text-accent"
                        checked={hasRole(user, "team member")}
                        onChange={(e) =>
                          handleRoleChange(
                            user.uid,
                            "team member",
                            e.target.checked,
                          )
                        }
                      />
                      <span className="ml-2 text-secondary">Team Member</span>
                    </label>
                    <label className="inline-flex items-center">
                      <input
                        type="checkbox"
                        className="form-checkbox h-5 w-5 text-accent"
                        checked={hasRole(user, "writer")}
                        onChange={(e) =>
                          handleRoleChange(user.uid, "writer", e.target.checked)
                        }
                      />
                      <span className="ml-2 text-secondary">Writer</span>
                    </label>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}
