"use client";

import React, { useCallback, useEffect, useState } from "react";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  orderBy,
  query,
  serverTimestamp,
  Timestamp,
  updateDoc,
} from "firebase/firestore";
import ProtectedRoute from "@/components/auth/protected-route";
import { db } from "@/lib/firebase";
import { useAuth } from "@/lib/auth-context";

export default function RoadmapManagementPage() {
  return (
    <ProtectedRoute>
      <RoadmapManagementContent />
    </ProtectedRoute>
  );
}

function RoadmapManagementContent() {
  const { user, loading: authLoading } = useAuth();
  const [milestones, setMilestones] = useState<
    Array<{
      id: string;
      title: string;
      description: string;
      date: string;
      status?: string;
      isPublic: boolean;
    }>
  >([]);
  const [loading, setLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [isEditing, setIsEditing] = useState(null);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    date: "",
    status: "planned",
    isPublic: true,
  });

  const fetchMilestones = useCallback(async () => {
    setLoading(true);
    try {
      const q = query(collection(db, "roadmap"), orderBy("date", "asc"));
      const querySnapshot = await getDocs(q);
      const milestonesData = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-member-access
        date:
          // eslint-disable-next-line @typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-member-access
          doc.data().date?.toDate?.()?.toISOString?.().split("T")[0] ??
          new Date().toISOString().split("T")[0],
      }));
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      setMilestones(milestonesData);
    } catch (error) {
      // Specify type for error
      console.error("Error fetching milestones:", error);
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      console.error("Error code:", error.code);
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      console.error("Error message:", error.message); // Log Firebase error message
      alert(
        "Failed to fetch milestones: " +
          (error instanceof Error ? error.message : String(error)),
      );
    } finally {
      setLoading(false);
    }
  }, [setLoading, setMilestones]);

  useEffect(() => {
    if (!authLoading && user) {
      const fetchData = async () => {
        await fetchMilestones();
      };
      void fetchData();
    }
  }, [authLoading, fetchMilestones, user]);

  const resetForm = () => {
    setFormData({
      title: "",
      description: "",
      date: "",
      status: "planned",
      isPublic: true,
    });
    setIsAdding(false);
    setIsEditing(null);
  };

  const handleInputChange = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value, type } = event.target;
    const checked = (event.target as HTMLInputElement).checked;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!formData.title || !formData.description || !formData.date) {
      alert("Please fill in all required fields");
      return;
    }

    try {
      const milestoneData = {
        ...formData,
        date: Timestamp.fromDate(new Date(formData.date)),
        updatedAt: serverTimestamp(),
      };

      if (isEditing) {
        // Update existing milestone
        await updateDoc(doc(db, "roadmap", isEditing), milestoneData);
      } else {
        // Add new milestone
        await addDoc(collection(db, "roadmap"), milestoneData);
      }

      resetForm();
      await fetchMilestones();
    } catch (error) {
      console.error("Error saving milestone:", error);
      alert(
        "Failed to save milestone: " +
          (error instanceof Error ? error.message : String(error)),
      );
    }
  };

  const handleEdit = (milestone: {
    id: string;
    title: string;
    description: string;
    date: string;
    status?: string;
    isPublic: boolean;
  }) => {
    setFormData({
      title: milestone.title,
      description: milestone.description,
      date: milestone.date,
      status: milestone.status ?? "planned",
      isPublic: milestone.isPublic,
    });
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    setIsEditing(milestone.id);
    setIsAdding(true);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this milestone?")) {
      try {
        await deleteDoc(doc(db, "roadmap", id));
        await fetchMilestones();
      } catch (error) {
        console.error("Error deleting milestone:", error);
        alert(
          "Failed to delete milestone: " +
            (error instanceof Error ? error.message : String(error)),
        );
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8 flex flex-col md:flex-row md:items-center md:justify-between">
        <h1 className="text-3xl font-bold text-primary">Roadmap Management</h1>
        <button
          onClick={() => setIsAdding(!isAdding)}
          className="mt-4 rounded-lg bg-accent px-6 py-2 text-white transition hover:opacity-90 md:mt-0"
        >
          {isAdding ? "Cancel" : "Add Milestone"}
        </button>
      </div>

      {isAdding && (
        <div className="mb-8 rounded-lg bg-secondary p-6">
          <h2 className="mb-4 text-xl font-semibold text-primary">
            {isEditing ? "Edit Milestone" : "Add New Milestone"}
          </h2>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <div>
                <label className="mb-1 block text-sm font-medium text-secondary">
                  Title
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-secondary">
                  Date
                </label>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-secondary">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="planned">Planned</option>
                  <option value="in-progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="delayed">Delayed</option>
                </select>
              </div>
              <div>
                <label className="mb-1 block text-sm font-medium text-secondary">
                  Visibility
                </label>
                <div className="mt-2 flex items-center">
                  <input
                    type="checkbox"
                    id="isPublic"
                    name="isPublic"
                    checked={formData.isPublic}
                    onChange={handleInputChange}
                    className="mr-2"
                  />
                  <label htmlFor="isPublic" className="text-secondary">
                    Make this milestone public
                  </label>
                </div>
              </div>
              <div className="md:col-span-2">
                <label className="mb-1 block text-sm font-medium text-secondary">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                  // @ts-ignore
                  onChange={handleInputChange}
                  rows={4}
                  className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                ></textarea>
              </div>
            </div>
            <div className="mt-6 flex justify-end">
              <button
                type="button"
                onClick={resetForm}
                className="border-border-primary hover:bg-primary/10 mr-4 rounded-lg border px-6 py-2 text-primary transition"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="rounded-lg bg-accent px-6 py-2 text-white transition hover:opacity-90"
              >
                {isEditing ? "Update Milestone" : "Add Milestone"}
              </button>
            </div>
          </form>
        </div>
      )}

      <div className="overflow-hidden rounded-lg bg-secondary">
        <h2 className="border-border-primary border-b p-6 text-xl font-semibold text-primary">
          Roadmap Milestones
        </h2>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-accent"></div>
          </div>
        ) : milestones.length === 0 ? (
          <div className="p-6 text-center text-secondary">
            No milestones found. Add your first milestone to get started.
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-primary">
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-secondary">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-secondary">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-secondary">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-secondary">
                    Visibility
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-secondary">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-border-primary divide-y">
                {milestones.map((milestone) => (
                  <tr key={milestone.id} className="hover:bg-primary/50">
                    <td className="whitespace-nowrap px-6 py-4 text-primary">
                      {milestone.title}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-secondary">
                      {new Date(milestone.date).toLocaleDateString()}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4">
                      <span
                        className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${
                          milestone.status === "completed"
                            ? "bg-green-500/20 text-green-400"
                            : milestone.status === "in-progress"
                              ? "bg-yellow-500/20 text-yellow-400"
                              : milestone.status === "delayed"
                                ? "bg-red-500/20 text-red-400"
                                : "bg-blue-500/20 text-blue-400"
                        }`}
                      >
                        {milestone.status?.replace("-", " ") ?? "planned"}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-secondary">
                      {milestone.isPublic ? "Public" : "Private"}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <button
                        onClick={() => handleEdit(milestone)}
                        className="hover:text-accent/80 mr-4 text-accent"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(milestone.id)}
                        className="text-red-500 hover:text-red-400"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
