"use client";

import { useCallback, useEffect, useState } from "react";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  serverTimestamp,
} from "firebase/firestore";
import ProtectedRoute from "@/components/auth/protected-route";
import { db } from "@/lib/firebase";

export default function SpreadsheetsPage() {
  return (
    <ProtectedRoute>
      <SpreadsheetsContent />
    </ProtectedRoute>
  );
}

function SpreadsheetsContent() {
  interface Spreadsheet {
    id: string;
    title: string;
    sheetUrl: string;
    createdAt: Date;
  }

  const [spreadsheets, setSpreadsheets] = useState<Spreadsheet[]>([]);
  const [loading, setLoading] = useState(true);
  const [title, setTitle] = useState("");
  const [googleSheetUrl, setGoogleSheetUrl] = useState("");
  const [selectedSheet, setSelectedSheet] = useState<Spreadsheet | null>(null);
  const [isAddingOpen, setIsAddingOpen] = useState(true);
  const [isSelectorOpen, setIsSelectorOpen] = useState(true);

  const fetchSpreadsheets = useCallback(async () => {
    try {
      const sheetsSnapshot = await getDocs(collection(db, "spreadsheets"));
      const sheetsList = sheetsSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-member-access,@typescript-eslint/no-unsafe-call
        createdAt: doc.data().createdAt?.toDate() ?? new Date(),
      }));
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      setSpreadsheets(sheetsList);
    } catch (error) {
      console.error("Error fetching spreadsheets:", error);
    } finally {
      setLoading(false);
    }
  }, [setLoading, setSpreadsheets]); // Add dependencies here

  useEffect(() => {
    void fetchSpreadsheets().then(() => {
      //
    });
  }, [fetchSpreadsheets]);

  const handleAddSheet = async () => {
    if (!title || !googleSheetUrl) {
      alert("Please enter a title and Google Sheet URL");
      return;
    }

    // Validate Google Sheet URL
    if (!googleSheetUrl.includes("docs.google.com/spreadsheets")) {
      alert("Please enter a valid Google Sheets URL");
      return;
    }

    try {
      // Extract the spreadsheet ID from the URL
      const urlParts = googleSheetUrl.split("/");
      const spreadsheetId = urlParts[5];

      // Construct the edit URL
      const editUrl = `https://docs.google.com/spreadsheets/d/${spreadsheetId}/edit`;

      await addDoc(collection(db, "spreadsheets"), {
        title,
        sheetUrl: editUrl,
        createdAt: serverTimestamp(),
      });

      // Reset form
      setTitle("");
      setGoogleSheetUrl("");

      // Refresh spreadsheets list
      await fetchSpreadsheets();

      alert("Spreadsheet added successfully!");
    } catch (error) {
      console.error("Error adding spreadsheet:", error);
      alert(
        "Failed to add spreadsheet: " +
          (error instanceof Error ? error.message : "Unknown error"),
      );
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this spreadsheet?")) {
      try {
        await deleteDoc(doc(db, "spreadsheets", id));
        if (selectedSheet?.id === id) {
          setSelectedSheet(null);
        }
        await fetchSpreadsheets();
      } catch (error) {
        console.error("Error deleting spreadsheet:", error);
        alert(
          "Failed to delete spreadsheet: " +
            (error instanceof Error ? error.message : "Unknown error"),
        );
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-8 text-3xl font-bold text-primary">Spreadsheets</h1>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
        <div className="lg:col-span-1">
          <div className="mb-4 rounded-lg bg-secondary p-6">
            <div className="mb-2 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-primary">
                Add Google Spreadsheet
              </h2>
              <button
                onClick={() => setIsAddingOpen(!isAddingOpen)}
                className="text-secondary hover:text-accent"
              >
                {isAddingOpen ? "▲" : "▼"}
              </button>
            </div>

            {isAddingOpen && (
              <>
                <div className="mb-4">
                  <label className="mb-1 block text-sm font-medium text-secondary">
                    Title
                  </label>
                  <input
                    type="text"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="Enter spreadsheet title"
                  />
                </div>

                <div className="mb-6">
                  <label className="mb-1 block text-sm font-medium text-secondary">
                    Google Sheet URL
                  </label>
                  <input
                    type="url"
                    value={googleSheetUrl}
                    onChange={(e) => setGoogleSheetUrl(e.target.value)}
                    className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                    placeholder="https://docs.google.com/spreadsheets/d/..."
                  />
                  <p className="mt-1 text-xs text-secondary">
                    Paste the URL of your Google Sheet (must be set to
                    &#34;Anyone with the link can edit&#34;)
                  </p>
                </div>

                <button
                  onClick={handleAddSheet}
                  disabled={!title || !googleSheetUrl}
                  className="rounded-lg bg-accent px-6 py-2 font-medium text-white transition hover:opacity-90 disabled:cursor-not-allowed disabled:opacity-70"
                >
                  Add Spreadsheet
                </button>
              </>
            )}
          </div>

          <div className="rounded-lg bg-secondary p-6">
            <div className="mb-2 flex items-center justify-between">
              <h2 className="text-xl font-semibold text-primary">
                Available Spreadsheets
              </h2>
              <button
                onClick={() => setIsSelectorOpen(!isSelectorOpen)}
                className="text-secondary hover:text-accent"
              >
                {isSelectorOpen ? "▲" : "▼"}
              </button>
            </div>

            {isSelectorOpen && (
              <>
                {loading ? (
                  <div className="flex justify-center py-4">
                    <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-accent"></div>
                  </div>
                ) : spreadsheets.length === 0 ? (
                  <p className="text-secondary">No spreadsheets added yet.</p>
                ) : (
                  <ul className="space-y-3">
                    {spreadsheets.map((sheet) => (
                      <li
                        key={sheet.id}
                        className={`relative rounded-lg border p-3 ${
                          selectedSheet?.id === sheet.id
                            ? "bg-accent/10 border-accent"
                            : "border-border-primary hover:border-border-secondary"
                        }`}
                      >
                        <div
                          className="cursor-pointer pr-8"
                          onClick={() => setSelectedSheet(sheet)}
                        >
                          <h3 className="font-medium text-primary">
                            {sheet.title}
                          </h3>
                          <p className="mt-1 text-xs text-secondary">
                            {sheet.createdAt.toLocaleDateString()}
                          </p>
                        </div>
                        <button
                          onClick={() => handleDelete(sheet.id)}
                          className="absolute right-3 top-3 text-red-500 hover:text-red-400"
                          aria-label="Delete spreadsheet"
                        >
                          ×
                        </button>
                      </li>
                    ))}
                  </ul>
                )}
              </>
            )}
          </div>
        </div>

        <div className="lg:col-span-3">
          <div className="h-full min-h-[600px] rounded-lg bg-secondary p-6">
            {selectedSheet ? (
              <div className="h-full">
                <div className="mb-4 flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-primary">
                    {selectedSheet.title}
                  </h2>
                  <a
                    href={selectedSheet.sheetUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-accent hover:underline"
                  >
                    Open in Google Sheets
                  </a>
                </div>
                <div className="h-[calc(100%-4rem)]">
                  <iframe
                    src={selectedSheet.sheetUrl}
                    className="h-full w-full rounded border-none"
                    title={selectedSheet.title}
                  />
                </div>
              </div>
            ) : (
              <div className="flex h-full flex-col items-center justify-center p-8 text-center">
                <div className="mb-4 text-6xl">📊</div>
                <h3 className="mb-2 text-xl font-semibold text-primary">
                  No Spreadsheet Selected
                </h3>
                <p className="max-w-md text-secondary">
                  Select a spreadsheet from the list or add a new one to view
                  its contents here.
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
