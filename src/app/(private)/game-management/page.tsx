"use client";

import React, { useCallback, useEffect, useState } from "react";
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDocs,
  serverTimestamp,
  updateDoc,
} from "firebase/firestore";
import { db } from "@/lib/firebase";

interface Game {
  id?: string;
  name: string;
  title: string;
  description: string;
  imageUrl: string;
  isPublic: boolean;
}

export default function GameManagement() {
  const [games, setGames] = useState<Game[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAdding, setIsAdding] = useState(false);
  const [formData, setFormData] = useState<Game>({
    name: "",
    title: "",
    description: "",
    imageUrl: "",
    isPublic: true,
  });
  const [editingGameId, setEditingGameId] = useState<string | null>(null);

  const fetchGames = useCallback(async () => {
    setLoading(true);
    try {
      const gamesSnapshot = await getDocs(collection(db, "games"));
      const gamesList = gamesSnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as Game[];
      setGames(gamesList);
    } catch (error) {
      console.error("Error fetching games:", error);
    } finally {
      setLoading(false);
    }
  }, [setLoading, setGames]);

  useEffect(() => {
    void fetchGames().then(() => {
      //
    });
  }, [fetchGames]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (
      !formData.name ||
      !formData.title ||
      !formData.description ||
      !formData.imageUrl
    ) {
      alert("Please fill in all fields.");
      return;
    }

    try {
      if (editingGameId) {
        // Update existing game
        await updateDoc(doc(db, "games", editingGameId), {
          ...formData,
          updatedAt: serverTimestamp(),
        });
      } else {
        // Add new game
        await addDoc(collection(db, "games"), {
          ...formData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
      }

      // Reset form and refresh games list
      setFormData({
        name: "",
        title: "",
        description: "",
        imageUrl: "",
        isPublic: true,
      });
      setIsAdding(false);
      setEditingGameId(null);
      await fetchGames();
    } catch (error) {
      console.error("Error saving game:", error);
      alert(
        "Failed to save game: " +
          (error instanceof Error ? error.message : "Unknown error"),
      );
    }
  };

  const handleEdit = (game: Game) => {
    setFormData({
      name: game.name,
      title: game.title,
      description: game.description,
      imageUrl: game.imageUrl,
      isPublic: game.isPublic,
    });
    setIsAdding(true);
    setEditingGameId(game.id ?? null);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this game?")) {
      try {
        await deleteDoc(doc(db, "games", id));
        await fetchGames();
      } catch (error) {
        console.error("Error deleting game:", error);
        alert(
          "Failed to delete game: " +
            (error instanceof Error ? error.message : "Unknown error"),
        );
      }
    }
  };

  return (
    <div className="rounded-lg bg-secondary p-6">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-xl font-semibold text-primary">Manage Games</h2>
        <button
          onClick={() => {
            setIsAdding(!isAdding);
            setEditingGameId(null);
            setFormData({
              name: "",
              title: "",
              description: "",
              imageUrl: "",
              isPublic: true,
            });
          }}
          className="rounded-lg bg-accent px-4 py-2 text-white transition hover:opacity-90"
        >
          {isAdding ? "Cancel" : "Add Game"}
        </button>
      </div>

      {isAdding && (
        <form onSubmit={handleSubmit} className="mb-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-1 block text-sm font-medium text-secondary">
                Name (URL-friendly)
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>
            <div>
              <label className="mb-1 block text-sm font-medium text-secondary">
                Title
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>
            <div>
              <label className="mb-1 block text-sm font-medium text-secondary">
                Image URL
              </label>
              <input
                type="text"
                name="imageUrl"
                value={formData.imageUrl}
                onChange={handleInputChange}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                required
              />
            </div>
            <div>
              <label className="mb-1 block text-sm font-medium text-secondary">
                Visibility
              </label>
              <div className="mt-2 flex items-center">
                <input
                  type="checkbox"
                  id="isPublic"
                  name="isPublic"
                  checked={formData.isPublic}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                <label htmlFor="isPublic" className="text-secondary">
                  Make this game public
                </label>
              </div>
            </div>
            <div className="col-span-full">
              <label className="mb-1 block text-sm font-medium text-secondary">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="border-border-primary w-full rounded-lg border bg-primary px-4 py-2 text-primary focus:outline-none focus:ring-2 focus:ring-primary"
                required
              ></textarea>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <button
              type="submit"
              className="rounded-lg bg-accent px-6 py-2 text-white transition hover:opacity-90"
            >
              {editingGameId ? "Update Game" : "Add Game"}
            </button>
          </div>
        </form>
      )}

      <div>
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-accent"></div>
          </div>
        ) : games.length === 0 ? (
          <p className="text-secondary">No games added yet.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="bg-primary">
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-secondary">
                    Title
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-secondary">
                    Visibility
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-secondary">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-border-primary divide-y">
                {games.map((game) => (
                  <tr key={game.id} className="hover:bg-primary/50">
                    <td className="whitespace-nowrap px-6 py-4 text-primary">
                      {game.title}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-secondary">
                      {game.isPublic ? "Public" : "Private"}
                    </td>
                    <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                      <button
                        onClick={() => handleEdit(game)}
                        className="hover:text-accent/80 mr-4 text-accent"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(game.id ?? "")}
                        className="text-red-500 hover:text-red-400"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
