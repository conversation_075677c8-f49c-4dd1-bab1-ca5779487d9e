import React from "react";
import Link from "next/link";

export default function JoinUsPage() {
  return (
    <>
      <div className="container mx-auto mb-12 rounded-3xl bg-secondary px-4 py-12 backdrop-blur">
        <h1 className="mb-8 text-center text-4xl font-bold text-accent md:text-5xl">
          Join Us
        </h1>
        <p className="px-2 pb-12 pb-2 text-center text-xl text-secondary">
          Interest Form can be found at the bottom of the page
        </p>

        <h2 className="py-7 text-center text-3xl text-secondary">
          What is Parallax Interactive?
        </h2>
        <p className="px-2 pb-2 text-secondary">
          Parallax Interactive is in charge of the publishing of other games
          along with the creation of our own! We recommend you read the entire
          Introduction page before viewing this page.
        </p>

        <h2 className="py-7 text-center text-3xl text-secondary">
          This job is the following:
        </h2>
        <ul>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Paid Work through Revenue Share primarily. However, thanks to our
            Publishing Service through Parallax Publishing, and our rapid
            creation of games, pay will come quickly. We pay via a LIVE revenue
            calculator which is a competitively based system that converts your
            total value in currency into a percentage using the data of other
            members. You get what you work for and what you are worth, which is
            all able to be viewed at any time by any member.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            NDA and Information sheet are required. The NDA, or Non-Disclosure
            Agreement, is crucial for our industry because of the immense story
            behind our game. More information can be seen about what is
            confidential and what is not upon signing.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            The average team member&#39;s age tends to be a little lower than
            other teams. You must be 16 years of age to join our team. We have
            no age maximum. Keep in mind, that our most prominent age range is
            18-22.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Minimal Time Requirements. We don&#39;t have any strict time
            requirements. We have very motivated members who are independent
            enough with high standards to work sufficiently on their own.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            We are a GitHub-based project that uses many other systems such as
            Clockify for clocking hours, and many Google systems such as Google
            Calendar. Our primary hand-to-hand communication is Discord,
            however, we also have a Developer Portal that we direct all of our
            team members to.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Relaxed hobby work, with a family-like team. We have never had any
            issues with immaturity or discrimination. More so, our work stays
            stress-free with plenty of time to complete tasks. Additionally,
            with our humbled management team, reaching out to a leader or even
            the Operational Director is extremely easy to do. The use of voice
            chat is essential and highly encouraged for all members, as that is
            a crucial part of any collaborative creative project.
          </li>
        </ul>

        <h2 className="py-7 text-center text-3xl text-secondary">
          What jobs do we have to offer?
        </h2>
        <p className="px-2 pb-2 text-secondary">
          We have the following jobs able to be worked:
        </p>
        <ul>
          <li className="whitespace-pre-wrap px-6 pb-2 text-primary">
            <strong className="whitespace-pre-wrap text-secondary">
              Game Developer:
            </strong>{" "}
            Working in Unreal Engine 5 to create and code the game using either
            Blueprints or C++ in the field you are comfortable with such as AI,
            basic features, cutscenes, etc. You could also go down the animation
            route!
          </li>
          <li className="whitespace-pre-wrap px-6 pb-2 text-primary">
            <strong className="whitespace-pre-wrap text-secondary">
              3D Modeler:
            </strong>{" "}
            Working in Blender or other modeling software to create any object
            category you are comfortable with. Either static objects, animals,
            foliage, etc. All in the hyper-realistic style. You could also
            specialize in being a Map artist (Environmental Creator).
          </li>
          <li className="whitespace-pre-wrap px-6 pb-2 text-primary">
            <strong className="whitespace-pre-wrap text-secondary">
              Story Writer:
            </strong>{" "}
            Work to write the story and lore for any of our projects.
          </li>
          <li className="whitespace-pre-wrap px-6 pb-2 text-primary">
            <strong className="whitespace-pre-wrap text-secondary">
              Community Manager:
            </strong>{" "}
            Our new paid version of Social Team! This role involves managing
            social media pages and managing the community. This includes
            individual jobs, such as posting daily questions, responding to
            comments, interacting with other partnered accounts, posting and
            scheduling content, posting Instagram stories, updates on X and
            Threads, organizing teasers, recording gameplay or assets, and more.
            All of this will be shared by the community managers on Instagram,
            Discord, X, Reddit, Threads, YouTube, Patreon, and possibly soon,
            TikTok. This is a new role and we are looking for new members
            quickly.
          </li>
          <li className="whitespace-pre-wrap px-6 pb-2 text-primary">
            <strong className="whitespace-pre-wrap text-secondary">
              Extra Departments:
            </strong>{" "}
            This includes miscellaneous departments such as music composing,
            video editing, or digital artists. Applying to this is different
            from the other roles. If none of the roles above describe you, feel
            free to pitch yourself and your job to us in the interest form
            below!
          </li>
        </ul>
        <p className="px-2 pb-2 text-accent">
          VOICE ACTING IS NOT INCLUDED HERE. Please do not use the Interest form
          for Voice Acting interest.
        </p>
        <p className="px-2 pb-2 text-accent">
          IF YOU HAVE TALENT THAT ISN&#39;T LISTED HERE: Please send an interest
          form still, as we are looking for far more than mentioned here, such
          as video editors, all artists (character artists, concept artists,
          general artists, etc), musicians, narrative designers, and so many
          more!
        </p>

        <h2 className="py-7 text-center text-3xl text-secondary">
          Revenue Share Agreement (Hourly Equivalent Value)
        </h2>
        <ul>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Game developer: $48/hr
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Animator: $47/hr
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            3D Modeler: $45/hr
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Level Designer: $44/hr
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Story Writer: $36/hr
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Community Manager: $30/hr
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Digital Artist: $42/hr
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Video Editor: $32/hr
          </li>
        </ul>

        <h2 className="py-7 text-center text-3xl text-secondary">
          Requirements
        </h2>
        <h3 className="pb-5 text-center text-xl text-secondary">
          Basic Requirements (A)
        </h3>
        <ul>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must be at least 16 years of age.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must have a working microphone.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must speak fluent English, able to converse in voice chat and
            message.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must be able to commit to this project and able to Voice Chat
            multiple times a week.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must have unrestricted access to the Internet, with the ability
            to access services such as Gmail, discord, and Google Drive, along
            with external sites such as Clockify.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must have a working unrestricted Google Account with 2FA
            enabled.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must show your full maturity and professionalism in this
            project.
          </li>
        </ul>

        <h3 className="pb-5 text-center text-xl text-secondary">
          Developer Requirements (B)
        </h3>
        <ul>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must have a quality computer. Able to handle Unreal Engine.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            At least one full year of experience in Unreal Engine 5.0 or above.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Fluent in using Unreal Engine’s blueprint language.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            Adequate in using C++ for Unreal Engine (you don&#39;t have to be
            perfect).
          </li>
        </ul>

        <h3 className="pb-5 text-center text-xl text-secondary">
          Modeler Requirements (C)
        </h3>
        <ul>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            You must have a quality computer. Able to handle Blender.
          </li>
          <li className="flex whitespace-pre-wrap px-6 pb-2 text-primary">
            At least one full year of experience in Blender.
          </li>
        </ul>

        <h2 className="py-7 text-center text-3xl text-secondary">
          What should my next steps be?
        </h2>
        <p className="px-2 pb-2 text-secondary">
          Parallax Interactive keeps a very high level of professionalism in the
          application portion. This is so we can have no issues with our members
          internally. We suggest you follow up with our advertisement here even
          if you aren&#39;t fully sold. We could fill an entire webpage with
          Q&As and information, or we could conversate directly with you via
          email and answer everything with you. So we encourage you to submit a
          form with some basic sorting information so we can reach out to you!
        </p>

        <div className="mt-10 flex justify-center space-x-4 text-center md:space-x-8 md:text-xl">
          <Link
            className="rounded-lg border-2 border-accent bg-primary px-8 py-3 font-medium text-white transition hover:scale-110 hover:bg-accent"
            href="https://docs.google.com/forms/d/e/1FAIpQLSc6NGWhZjc1dOdWa_fb44aqTS-fO8OIlvEBg9wt_BCbbRka8g/viewform"
            target="_blank"
          >
            Go to the Interest Form
          </Link>
        </div>
      </div>
    </>
  );
}
