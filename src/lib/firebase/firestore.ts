import {
  addDoc,
  arrayUnion,
  collection,
  deleteDoc,
  doc,
  getDocs,
  orderBy,
  query,
  serverTimestamp,
  Timestamp,
  updateDoc,
  writeBatch,
} from "firebase/firestore";
import { db } from "../firebase";
import type {
  KanbanColumn,
  KanbanTask,
  TaskNote, TaskPriority,
  UserProfile
} from "@/types/kanban";
import { v4 as uuidv4 } from "uuid";

export const getKanbanColumns = async (): Promise<KanbanColumn[]> => {
  if (!db) {
    console.log("Firebase not available, returning empty columns array");
    return [];
  }
  const q = query(collection(db, "kanban_columns"), orderBy("order"));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(
    (doc) => ({ id: doc.id, ...doc.data() }) as KanbanColumn,
  );
};

export const addColumn = async (
  columnData: Omit<KanbanColumn, "id">,
): Promise<string> => {
  if (!db) {
    throw new Error("Firebase not available");
  }
  const docRef = await addDoc(collection(db, "kanban_columns"), columnData);
  return docRef.id;
};

export const updateColumn = async (
  columnId: string,
  updates: Partial<KanbanColumn>,
): Promise<void> => {
  const docRef = doc(db, "kanban_columns", columnId);
  await updateDoc(docRef, updates);
};

export const deleteColumn = async (columnId: string): Promise<void> => {
  const docRef = doc(db, "kanban_columns", columnId);
  await deleteDoc(docRef);
};

export const updateColumnOrderBatch = async (
  updates: { id: string; order: number }[],
): Promise<void> => {
  const batch = writeBatch(db);
  updates.forEach((update) => {
    const docRef = doc(db, "kanban_columns", update.id);
    batch.update(docRef, { order: update.order });
  });
  await batch.commit();
};

export const deleteTasksByColumn = async (columnId: string): Promise<void> => {
  const tasksQuery = query(collection(db, "kanban_tasks"));
  const tasksSnapshot = await getDocs(tasksQuery);
  const batch = writeBatch(db);

  tasksSnapshot.docs.forEach((taskDoc) => {
    const task = taskDoc.data();
    if (task.columnId === columnId) {
      batch.delete(taskDoc.ref);
    }
  });

  await batch.commit();
};


export const getKanbanTasks = async (): Promise<KanbanTask[]> => {
  if (!db) {
    console.log("Firebase not available, returning empty tasks array");
    return [];
  }
  const q = query(collection(db, "kanban_tasks"), orderBy("order"));
  const snapshot = await getDocs(q);
  return snapshot.docs.map(
    (doc) =>
      ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt as Timestamp,
        updatedAt: doc.data().updatedAt as Timestamp,
        priority: (doc.data()?.priority as TaskPriority) ?? "medium"
      }) as KanbanTask,
  );
};

export const addTask = async (
  taskData: Omit<KanbanTask, "id" | "createdAt" | "updatedAt" | "notes">,
): Promise<string> => {
  if (!db) {
    throw new Error("Firebase not available");
  }

  const newTask = {
    title: taskData.title,
    description: taskData.description ?? "",
    columnId: taskData.columnId,
    assignedTeam: taskData.assignedTeam ?? "All",
    assignedTo: taskData.assignedTo ?? null,
    assignedUserName: taskData.assignedUserName ?? null,
    createdBy: taskData.createdBy,
    createdByName: taskData.createdByName ?? null,
    order: taskData.order,
    priority: taskData.priority || "medium",
    dueDate: taskData.dueDate ?? null,
    notes: [],
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
  };

  const docRef = await addDoc(collection(db, "kanban_tasks"), newTask);
  return docRef.id;
};

export const updateTask = async (
  taskId: string,
  updates: Partial<KanbanTask>,
): Promise<void> => {
  const taskRef = doc(db, "kanban_tasks", taskId);
  await updateDoc(taskRef, { ...updates, updatedAt: serverTimestamp() });
};

export const deleteTask = async (taskId: string): Promise<void> => {
  const taskRef = doc(db, "kanban_tasks", taskId);
  await deleteDoc(taskRef);
};

export const updateTaskOrderBatch = async (
  tasksToUpdate: { id: string; order: number; columnId?: string }[],
): Promise<void> => {
  const batch = writeBatch(db);
  tasksToUpdate.forEach((taskUpdate) => {
    const taskRef = doc(db, "kanban_tasks", taskUpdate.id);
    const updateData: Partial<KanbanTask> = { order: taskUpdate.order };
    if (taskUpdate.columnId) {
      updateData.columnId = taskUpdate.columnId;
    }
    updateData.updatedAt = serverTimestamp() as Timestamp;
    batch.update(taskRef, updateData);
  });
  await batch.commit();
};

export const getAssignableUsers = async (): Promise<UserProfile[]> => {
  try {
    if (!db) {
      console.log("Firebase not available, returning empty users array");
      return [];
    }
    const usersCollection = collection(db, "users");
    const snapshot = await getDocs(usersCollection);
    if (snapshot.empty) {
      console.warn("No users found in 'users' collection for task assignment.");
      return [];
    }
    return snapshot.docs.map(
      (doc) =>
        ({
          uid: doc.id,
          ...doc.data(),
        }) as UserProfile,
    );
  } catch (error) {
    console.error("Error fetching assignable users:", error);
    return [];
  }
};

export const addTaskNote = async (
  taskId: string,
  noteText: string,
  userId: string,
  userName?: string,
): Promise<TaskNote> => {
  const taskRef = doc(db, "kanban_tasks", taskId);
  const newNote: TaskNote = {
    id: uuidv4(),
    text: noteText,
    userId,
    userName: userName ?? "User",
    timestamp: Timestamp.now(),
  };
  await updateDoc(taskRef, {
    notes: arrayUnion(newNote),
    updatedAt: serverTimestamp(),
  });
  return newNote;
};
