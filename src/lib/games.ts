import {
  collection,
  doc,
  getDoc,
  getDocs,
  orderBy,
  query,
  where,
} from "firebase/firestore";
import { db } from "@/lib/firebase";

export interface Game {
  id?: string;
  name: string;
  title: string;
  description: string;
  imageUrl?: string;
  isPublic?: boolean;
  releaseDate?: Date;
  coverImage?: string;
}

export async function getPublicGamesIDs(): Promise<{ name: string }[]> {
  try {
    const q = query(
      collection(db, "games"),
      where("isPublic", "==", true),
      orderBy("releaseDate", "desc"),
    );
    const snapshot = await getDocs(q);
    const params = snapshot.docs.map((doc) => ({
      name: doc.id,
    }));
    console.log("Successfully fetched game names:", params);
    // Important: Return at least one parameter object even if array is empty
    if (params.length === 0) {
      console.log("No blog posts found, adding placeholder parameter");
      return [{ name: "placeholder" }];
    }
    return params;
  } catch (error) {
    console.error("Error fetching game names for params:", error);
    // Return at least one placeholder name to prevent build failure
    return [{ name: "placeholder" }];
  }
}

export async function getPublicGames(): Promise<Game[] | null> {
  try {
    const q = query(
      collection(db, "games"),
      where("isPublic", "==", true),
      orderBy("releaseDate", "desc"),
    );
    const snapshot = await getDocs(q);
    const params = snapshot.docs.map((doc) => ({
      name: doc.id,
      title: doc.data().title as string,
      description: doc.data().description as string,
      coverImage: doc.data().coverImage as string | undefined,
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-member-access
      releaseDate: doc.data().releaseDate?.toDate(),
      isPublic: doc.data().isPublic as boolean,
    }));
    console.log("Successfully fetched game names:", params);
    // Important: Return at least one parameter object even if array is empty
    if (params.length === 0) {
      console.log("No blog posts found, adding placeholder parameter");
      return [
        {
          name: "placeholder",
          title: "Coming Soon",
          description: "Our games are coming soon!",
          imageUrl: "",
          isPublic: true,
          releaseDate: new Date(),
        },
      ];
    }
    return params;
  } catch (error) {
    console.error("Error fetching game names for params:", error);
    // Return at least one placeholder name to prevent build failure
    return [
      {
        name: "placeholder",
        title: "Coming Soon",
        description: "Our games are coming soon!",
        imageUrl: "",
        isPublic: true,
        releaseDate: new Date(),
      },
    ];
  }
}

export async function getGame(name: string): Promise<Game | null> {
  try {
    // Special handling for placeholder during build
    if (name === "placeholder") {
      return {
        name: "placeholder",
        title: "Coming Soon",
        description: "Our games are coming soon!",
        imageUrl: "",
        isPublic: true,
        releaseDate: new Date(),
      };
    }

    const docRef = name ? doc(db, "games", name) : null;
    if (!docRef) return null;
    const docSnap = await getDoc(docRef);

    if (docSnap.exists() && docSnap.data()) {
      const data = docSnap.data();
      if (data.isPublic) {
        return {
          name: docSnap.id,
          title: data.title as string,
          description: data.description as string,
          coverImage: data.coverImage as string | undefined,
          // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment,@typescript-eslint/no-unsafe-call,@typescript-eslint/no-unsafe-member-access
          releaseDate: data.releaseDate?.toDate(),
          isPublic: data.isPublic as boolean,
          // Map other properties as needed
        };
      }
    }

    return null;
  } catch (error) {
    console.error("Error fetching game:", error);
    return null;
  }
}
