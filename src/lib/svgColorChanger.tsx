"use client";

import React, { type ReactNode, useEffect, useRef, useState } from "react";

// Types for the color changer
type SelectorStyles = {
  fill?: string;
  stroke?: string;
  strokeWidth?: string | number;
};

type ColorChangeMap = Record<string, SelectorStyles>;

interface SvgColorChangerProps {
  children: ReactNode;
  colorMap: ColorChangeMap;
  className?: string;
  style?: React.CSSProperties;
  transitionDuration?: number;
}

/**
 * Hook to apply color changes to an SVG
 * @param svgRef Reference to the SVG element
 * @param colorMap Map of selectors to color properties
 * @param transitionDuration Duration of color transition in ms
 */
export const useSvgColorChanger = (
  svgRef: React.RefObject<SVGSVGElement | HTMLDivElement>,
  colorMap: ColorChangeMap,
  transitionDuration = 200,
) => {
  useEffect(() => {
    if (!svgRef.current) return;

    // Get the SVG element - either directly or find it inside the div
    const svgElement =
      svgRef.current instanceof SVGSVGElement
        ? svgRef.current
        : svgRef.current.querySelector("svg");

    if (!svgElement) return;

    // Apply color changes to each selector
    Object.entries(colorMap).forEach(([selector, properties]) => {
      const elements = svgElement.querySelectorAll(selector);

      elements.forEach((el) => {
        // Apply transition if needed
        if (transitionDuration > 0) {
          (el as SVGElement).style.transition =
            `fill ${transitionDuration}ms, stroke ${transitionDuration}ms, stroke-width ${transitionDuration}ms`;
        }

        // Apply the properties
        if (properties.fill !== undefined) {
          (el as SVGElement).style.fill = properties.fill;
        }

        if (properties.stroke !== undefined) {
          (el as SVGElement).style.stroke = properties.stroke;
        }

        if (properties.strokeWidth !== undefined) {
          (el as SVGElement).style.strokeWidth = String(properties.strokeWidth);
        }
      });
    });
  }, [svgRef, colorMap, transitionDuration]);
};

/**
 * Component that wraps an SVG and changes its colors
 */
export const SvgColorChanger: React.FC<SvgColorChangerProps> = ({
  children,
  colorMap,
  className = "",
  style = {},
  transitionDuration = 200,
}) => {
  const svgContainerRef = useRef<HTMLDivElement>(null);

  // Apply the color changes using the hook
  useSvgColorChanger(svgContainerRef, colorMap, transitionDuration);

  return (
    <div
      ref={svgContainerRef}
      className={`svg-color-changer ${className}`}
      style={style}
    >
      {children}
    </div>
  );
};

/**
 * Component for working with SVG files
 */
interface SvgFileColorChangerProps
  extends Omit<SvgColorChangerProps, "children"> {
  src: string;
  width?: number | string;
  height?: number | string;
  alt?: string;
  onLoad?: () => void;
}

export const SvgFileColorChanger: React.FC<SvgFileColorChangerProps> = ({
  src,
  colorMap,
  className = "",
  style = {},
  width = "100%",
  height = "100%",
  alt = "SVG Image",
  transitionDuration = 200,
  onLoad,
}) => {
  const svgContainerRef = useRef<HTMLDivElement>(null);
  const [svgContent, setSvgContent] = useState<string>("");

  // Fetch and load the SVG file
  useEffect(() => {
    const fetchSvg = async () => {
      try {
        const response = await fetch(src);
        if (!response.ok) {
          console.error("Failed to load SVG");
        }

        const svgText = await response.text();
        setSvgContent(svgText);
        onLoad?.();
      } catch (error) {
        console.error("Error loading SVG:", error);
      }
    };

    void fetchSvg().then(() => {
      // Clean up the SVG content when the component unmounts
      return () => {
        setSvgContent("");
      };
    });
  }, [src, onLoad]);

  // Apply color changes after SVG is loaded
  useSvgColorChanger(svgContainerRef, colorMap, transitionDuration);

  return (
    <div
      ref={svgContainerRef}
      className={`svg-file-color-changer ${className}`}
      style={{ ...style, width, height }}
      dangerouslySetInnerHTML={{ __html: svgContent }}
      aria-label={alt}
      role="img"
    />
  );
};

/**
 * Theme switcher component for SVGs
 */
export function SvgThemeSwitcher<T extends Record<string, ColorChangeMap>>({
  themes,
  initialTheme,
  children,
  className = "",
  style = {},
  transitionDuration = 300,
}: {
  themes: T;
  initialTheme: keyof T;
  children: ReactNode;
  className?: string;
  style?: React.CSSProperties;
  transitionDuration?: number;
}) {
  const [currentTheme, setCurrentTheme] = useState<keyof T>(initialTheme);
  const svgContainerRef = useRef<HTMLDivElement>(null);

  // Apply current theme
  useSvgColorChanger(
    svgContainerRef,
    themes[currentTheme as string] ?? {},
    transitionDuration,
  );

  // Expose theme switching function
  useEffect(() => {
    // Make the function available globally
    (
      window as Window & { switchSvgTheme?: (themeName: keyof T) => boolean }
    ).switchSvgTheme = (themeName: keyof T) => {
      if (themes[themeName]) {
        setCurrentTheme(themeName);
        return true;
      }
      return false;
    };

    return () => {
      delete (
        window as Window & { switchSvgTheme?: (themeName: keyof T) => boolean }
      ).switchSvgTheme;
    };
  }, [themes]);

  return (
    <div
      ref={svgContainerRef}
      className={`svg-theme-switcher ${className}`}
      style={style}
    >
      {children}
    </div>
  );
}

/**
 * Hook to create a controlled SVG theme switcher
 */
export const useSvgThemeSwitcher = <T extends Record<string, ColorChangeMap>>(
  themes: T,
  initialTheme: keyof T,
) => {
  const [currentTheme, setCurrentTheme] = useState<keyof T>(initialTheme);

  const switchTheme = (themeName: keyof T) => {
    if (themes[themeName]) {
      setCurrentTheme(themeName);
      return true;
    }
    return false;
  };

  return {
    currentTheme,
    switchTheme,
    colorMap: themes[currentTheme as string],
    themes,
  };
};
