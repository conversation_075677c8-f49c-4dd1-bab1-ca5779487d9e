import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

const firebaseConfig = {
  apiKey: "AIzaSyAnDq8RZF_aov3jLoajTKrfsh0P-8HrsHI",
  authDomain: "parallax-interactive-b0d99.firebaseapp.com",
  projectId: "parallax-interactive-b0d99",
  storageBucket: "parallax-interactive-b0d99.firebasestorage.app",
  messagingSenderId: "697881328211",
  appId: "1:697881328211:web:396994e6c5511b726ae4a6",
  measurementId: "G-YTWFWL1V87",
};

const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);
