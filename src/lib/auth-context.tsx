"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import {
  onAuthStateChanged,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
} from "firebase/auth";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { auth, db } from "./firebase";

interface UserData {
  uid: string;
  email: string | null;
  roles: string[];
}

const AuthContext = createContext<{
  user: UserData | null;
  loading: boolean;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
}>({
  user: null,
  loading: true,
  signIn: async (email: string, password: string) => {
    return signInWithEmailAndPassword(auth, email, password);
  },
  signOut: async () => {
    return;
  },
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      if (user) {
        // Handle async operations in a separate function
        const updateUserData = async () => {
          const userDocRef = doc(db, "users", user.uid);
          const userDocSnap = await getDoc(userDocRef);

          if (userDocSnap.exists()) {
            setUser({
              ...user,
              roles: (userDocSnap.data() as { roles?: string[] })?.roles ?? [],
            });
          } else {
            await setDoc(doc(db, "users", user.uid), {
              uid: user.uid,
              email: user.email,
              roles: [],
            });
            setUser({
              ...user,
              roles: [],
            });
          }
          setLoading(false);
        };
        void updateUserData().catch((error) => {
          console.error("Error updating user data:", error);
          setLoading(false);
        });
      } else {
        setUser(null);
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    return signInWithEmailAndPassword(auth, email, password);
  };

  const signOut = async () => {
    return firebaseSignOut(auth);
  };

  return (
    <AuthContext.Provider value={{ user, loading, signIn, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);
