"use client";

import React, {
  type CSSProperties,
  type RefObject,
  useEffect,
  useState,
} from "react";
import Image from "next/image";

// Handle server-side rendering
const isBrowser = typeof window !== "undefined";

/**
 * Custom hook for parallax scrolling effects
 * @param ref - Reference to the element to apply parallax to
 * @param speed - Speed multiplier for the parallax effect (default: 0.5)
 */
export function useParallax(ref: RefObject<HTMLElement>, speed = 0.5): void {
  useEffect(() => {
    if (!isBrowser || !ref.current) return;

    const handleScroll = () => {
      const scrollPosition = window.pageYOffset;
      if (ref.current) {
        ref.current.style.transform = `translateY(${scrollPosition * speed}px)`;
      }
    };

    // Set initial position
    handleScroll();

    // Add scroll event listener
    window.addEventListener("scroll", handleScroll);

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [ref, speed]);
}

interface ParallaxElementProps {
  children: React.ReactNode;
  className?: string;
  speed?: number;
  style?: CSSProperties;
  containerStyle?: CSSProperties;
  fullWidth?: boolean;
  fullHeight?: boolean;
}

/**
 * Component for easy parallax implementation
 */
export const ParallaxElement: React.FC<ParallaxElementProps> = ({
  children,
  className = "",
  speed = 0.5,
  style = {},
  containerStyle = {},
  fullWidth = true,
  fullHeight = false,
}) => {
  const [ref, setRef] = useState<HTMLDivElement | null>(null);

  useParallax({ current: ref } as RefObject<HTMLElement>, speed);

  // Default container styles to position content properly
  const defaultContainerStyle: CSSProperties = {
    position: "absolute",
    overflow: "hidden",
    width: fullWidth ? "100%" : undefined,
    height: fullHeight ? "100vh" : undefined,
    ...containerStyle,
  };

  // Default styles for the parallax element
  const defaultStyle: CSSProperties = {
    position: "relative",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    ...style,
  };

  return (
    <div className="parallax-container" style={defaultContainerStyle}>
      <div
        ref={setRef}
        className={`parallax-element ${className}`}
        style={defaultStyle}
      >
        {children}
      </div>
    </div>
  );
};

/**
 * Component specifically for parallax background images
 */
export const ParallaxBackground: React.FC<{
  src: string;
  alt?: string;
  speed?: number;
  containerClassName?: string;
  containerStyle?: CSSProperties;
  imageStyle?: CSSProperties;
}> = ({
  src,
  alt = "Background image",
  speed = 0.5,
  containerClassName = "",
  containerStyle = {},
  imageStyle = {},
}) => {
  return (
    <ParallaxElement
      speed={speed}
      containerStyle={containerStyle}
      className={containerClassName}
      fullWidth
      fullHeight
    >
      <Image
        src={src}
        alt={alt}
        fill
        style={{
          objectFit: "cover",
          minWidth: "100%",
          minHeight: "100%",
          ...imageStyle,
        }}
      />
    </ParallaxElement>
  );
};

/**
 * Component specifically for parallax background videos
 */
export const ParallaxVideo: React.FC<{
  src: string;
  alt?: string;
  speed?: number;
  containerClassName?: string;
  containerStyle?: CSSProperties;
  imageStyle?: CSSProperties;
}> = ({
  src,
  speed = 0.5,
  containerClassName = "",
  containerStyle = {},
  imageStyle = {},
}) => {
  return (
    <ParallaxElement
      speed={speed}
      containerStyle={containerStyle}
      className={containerClassName}
      fullWidth
      fullHeight
    >
      <video
        src={src}
        autoPlay
        loop
        muted
        controls={false}
        style={{
          objectFit: "cover",
          minWidth: "100%",
          minHeight: "100%",
          ...imageStyle,
        }}
      />
    </ParallaxElement>
  );
};
