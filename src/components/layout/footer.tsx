import Link from "next/link";
import Get<PERSON><PERSON> from "./get-logo";
import { SvgFileColorChanger } from "@/lib/svgColorChanger";

export default function Footer() {
  return (
    <footer className="bg-secondary py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
          <div className="md:col-span-1">
            <Link href="/" className="mb-4 flex items-center">
              <GetLogo />
            </Link>
            <p className="mb-6 text-secondary">
              Creating immersive gaming experiences that push the boundaries of
              imagination.
            </p>
            <div className="flex flex-wrap">
              {[
                { name: "Discord", url: "https://discord.gg/Gj3ZhWKjUN" },
                {
                  name: "GitHub",
                  url: "https://github.com/Parallax-Interactive",
                },
                {
                  name: "YouTube",
                  url: "https://www.youtube.com/@parallaxinteractiveinfo",
                },
                {
                  name: "Instagram",
                  url: "https://www.instagram.com/parallaxinteractive.info/",
                },
                {
                  name: "Merch",
                  url: "https://parallax-interactive.printify.me/",
                },
                { name: "Appeal", url: "https://forms.gle/NG5F4mi2WyYXckuL9" },
              ].map((social) => (
                <a
                  key={social.name}
                  href={social.url}
                  target="_blank"
                  className="flex h-8 w-8 items-center justify-center rounded-full transition hover:bg-accent"
                  aria-label={social.name}
                >
                  <SvgFileColorChanger
                    src={`/images/socials/${social.name.toLowerCase() + "-logo.svg"}`}
                    width={25}
                    height={25}
                    colorMap={{}}
                  />
                </a>
              ))}
            </div>
          </div>

          <div>
            <h3 className="mb-4 text-lg font-semibold text-primary">
              Quick Links
            </h3>
            <ul className="space-y-3">
              {[
                { name: "Home", href: "/" },
                { name: "About Us", href: "/about" },
                {
                  name: "Join Our Team",
                  href: "/join-us",
                },
                { name: "Our Games", href: "/games" },
                { name: "Roadmap", href: "/roadmap" },
                { name: "Blog", href: "/blog" },
              ]
                .filter((link) => link.href)
                .map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-secondary transition hover:text-accent"
                      target={link.href ?? undefined}
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
            </ul>
          </div>

          <div>
            <h3 className="mb-4 text-lg font-semibold text-primary">Support</h3>
            <ul className="space-y-3">
              {[
                {
                  name: "Donate",
                  href: "https://discord.com/servers/parallax-interactive-1036739036427071498",
                  target: "_blank",
                },
                {
                  name: "Contact Us",
                  href: "https://docs.google.com/forms/d/e/1FAIpQLScVQm5GxwtF3Wnn3xyI1h4nUoQdskxr-enlA6ZZ7Fy0Mj-tEw/viewform",
                  target: "_blank",
                },
                // { name: "FAQ", href: "/faq" },
                { name: "Privacy Policy", href: "/privacy" },
                { name: "Terms of Service", href: "/terms" },
              ].map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-secondary transition hover:text-accent"
                    target={link.target ?? undefined}
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="mb-4 text-lg font-semibold text-primary">Discord</h3>
            <p className="mb-4 text-secondary">
              Join our Discord server to get updates about our games and
              development process.
            </p>
            <div className="flex justify-center space-x-4 text-center">
              <a
                href="https://discord.gg/9YK7rgKZJa"
                target="_blank"
                className="w-full rounded-lg bg-accent py-2 font-medium text-white transition hover:opacity-90"
              >
                Join
              </a>
            </div>
          </div>
        </div>
      </div>
      <div className="mt-12 w-full border-t border-primary pt-6 text-center text-secondary">
        <p>© 2025 Parallax Interactive. All rights reserved.</p>
      </div>
    </footer>
  );
}
