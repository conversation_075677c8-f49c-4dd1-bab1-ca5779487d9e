"use client";

import Link from "next/link";
import Get<PERSON><PERSON> from "./get-logo";
import { SvgFileColorChanger } from "@/lib/svgColorChanger";

export default function SocialBar() {
  return (
    <div className="fixed bottom-0 left-1/2 z-50 mx-auto flex w-fit -translate-x-1/2 grid-cols-1 items-center justify-between gap-4 rounded-full border border-primary px-4 py-3 backdrop-blur md:-right-1/2 md:top-1/2 md:grid md:-translate-y-1/2">
      <div className="items-center space-x-4">
        <Link href="/" className="flex items-center">
          <GetLogo isText={false} />
        </Link>
      </div>
      <div className="mb-2 flex items-center space-x-4 rounded-full p-2 md:block">
        {[
          { name: "Discord", url: "https://discord.gg/Gj3ZhWKjUN" },
          { name: "GitHub", url: "https://github.com/Parallax-Interactive" },
          {
            name: "YouTube",
            url: "https://www.youtube.com/@parallaxinteractiveinfo",
          },
          {
            name: "Instagram",
            url: "https://www.instagram.com/parallaxinteractive.info/",
          },
          { name: "Merch", url: "https://parallax-interactive.printify.me/" },
          { name: "Appeal", url: "https://forms.gle/NG5F4mi2WyYXckuL9" },
        ].map((social) => (
          <Link
            key={social.name}
            href={social.url}
            target="_blank"
            className="items-center justify-center rounded-full text-center text-accent transition-all hover:scale-125"
            aria-label={social.name}
          >
            <SvgFileColorChanger
              src={`/images/socials/${social.name.toLowerCase() + "-logo.svg"}`}
              width={25}
              height={25}
              colorMap={{}}
            />
          </Link>
        ))}
      </div>
    </div>
  );
}
