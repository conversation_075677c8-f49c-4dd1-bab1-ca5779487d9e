"use client";

import { useTheme } from "next-themes";
import Image from "next/image";
import { useEffect } from "react";

export default function GetLogo(props: { isText?: boolean }) {
  const { isText = true } = props;
  const { theme, setTheme, systemTheme } = useTheme();

  useEffect(() => {
    if (theme === "system") {
      setTheme(systemTheme ?? "dark");
    }
  }, [theme, setTheme, systemTheme]);

  return (
    <div className="flex items-center space-x-2">
      <Image
        src={`${theme === "light" ? "/images/DarkLogo.png" : "/images/LightLogo.png"}`}
        alt="Parallax Interactive Logo"
        width={40}
        height={40}
      />
      {isText && (
        <span className="text-sml font-bold text-primary">
          Parallax Interactive
        </span>
      )}
    </div>
  );
}
