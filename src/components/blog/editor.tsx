"use client";

import {
  type Editor as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Editor<PERSON>ontent,
  useEditor,
} from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import Image from "@tiptap/extension-image";

const MenuBar = ({ editor }: { editor: TiptapEditor | null }) => {
  if (!editor) {
    return null;
  }

  return (
    <div className="border-border-primary mb-2 flex flex-wrap gap-1 rounded-t-lg border bg-primary p-2">
      <button
        onClick={() => editor.chain().focus().toggleBold().run()}
        disabled={!editor.can().chain().focus().toggleBold().run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("bold")
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        <span className="font-bold">B</span>
      </button>
      <button
        onClick={() => editor.chain().focus().toggleItal<PERSON>().run()}
        disabled={!editor.can().chain().focus().toggleItalic().run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("italic")
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        <span className="italic">I</span>
      </button>
      <button
        onClick={() => editor.chain().focus().toggleStrike().run()}
        disabled={!editor.can().chain().focus().toggleStrike().run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("strike")
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        <span className="line-through">S</span>
      </button>
      <button
        onClick={() => editor.chain().focus().toggleCode().run()}
        disabled={!editor.can().chain().focus().toggleCode().run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("code")
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        <span className="font-mono">{"<>"}</span>
      </button>
      <div className="bg-border-primary mx-1 h-6 w-px self-center"></div>
      <button
        onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("heading", { level: 1 })
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        H1
      </button>
      <button
        onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("heading", { level: 2 })
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        H2
      </button>
      <button
        onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("heading", { level: 3 })
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        H3
      </button>
      <div className="bg-border-primary mx-1 h-6 w-px self-center"></div>
      <button
        onClick={() => editor.chain().focus().toggleBulletList().run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("bulletList")
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        • List
      </button>
      <button
        onClick={() => editor.chain().focus().toggleOrderedList().run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("orderedList")
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        1. List
      </button>
      <button
        onClick={() => editor.chain().focus().toggleCodeBlock().run()}
        className={`hover:bg-accent/20 rounded p-2 ${
          editor.isActive("codeBlock")
            ? "bg-accent/20 text-accent"
            : "text-secondary"
        }`}
      >
        Code Block
      </button>
      <div className="bg-border-primary mx-1 h-6 w-px self-center"></div>
      <button
        onClick={() => editor.chain().focus().setHorizontalRule().run()}
        className="hover:bg-accent/20 rounded p-2 text-secondary"
      >
        Divider
      </button>
      <button
        onClick={() => {
          const url = window.prompt("Enter image URL");
          if (url) {
            editor.chain().focus().setImage({ src: url }).run();
          }
        }}
        className="hover:bg-accent/20 rounded p-2 text-secondary"
      >
        Image
      </button>
    </div>
  );
};

export default function RichTextEditor({
  value,
  onChange,
}: {
  value: string;
  onChange: (content: string) => void;
}) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: true,
        },
      }),
      Placeholder.configure({
        placeholder: "Write something amazing...",
        emptyEditorClass: "is-editor-empty",
        emptyNodeClass: "is-empty",
        showOnlyWhenEditable: true,
        showOnlyCurrent: true,
      }),
      Image.configure({
        inline: true,
        HTMLAttributes: {
          class: "max-w-full",
        },
        allowBase64: true,
      }),
    ],
    content: value,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: "h-full outline-none prose prose-invert min-h-[350px]",
        style: "height: 100%",
      },
    },
  });

  return (
    <div className="flex h-full w-full flex-col rounded-lg">
      {editor && <MenuBar editor={editor} />}
      <EditorContent
        editor={editor}
        className="prose prose-invert min-h-[350px] max-w-none rounded-b-lg border border-primary bg-primary p-4 focus:outline-none"
      />
    </div>
  );
}
