"use client";

import React, { useEffect, useState } from "react";
import type {
  KanbanColumn,
  KanbanTask,
  TaskPriority,
  UserProfile,
} from "@/types/kanban";
import { Calendar, FileText, Flag, Plus, User, X } from "lucide-react";
import { Timestamp } from "firebase/firestore";
import { format, formatDistanceToNow } from "date-fns";

interface TaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (task: KanbanTask) => Promise<void>;
  onDelete?: (taskId: string) => Promise<void>;
  onAddNote: (taskId: string, noteText: string) => Promise<void>;
  task?: KanbanTask | null;
  columns: KanbanColumn[];
  users?: UserProfile[];
  initialColumnId?: string;
}

const priorityOptions: {
  value: TaskPriority;
  label: string;
  icon: string;
}[] = [
  { value: "low", label: "Low", icon: "🟢" },
  { value: "medium", label: "Medium", icon: "🟡" },
  { value: "high", label: "High", icon: "🟠" },
  { value: "critical", label: "Critical", icon: "🔴" },
];

const TaskModal: React.FC<TaskModalProps> = ({
                                               isOpen,
                                               onClose,
                                               onSave,
                                               onDelete,
                                               onAddNote,
                                               task,
                                               columns,
                                               users = [],
                                               initialColumnId,
                                             }) => {
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [columnId, setColumnId] = useState("");
  const [assignedTo, setAssignedTo] = useState<string>("");
  const [assignedTeam, setAssignedTeam] = useState<string>("All");
  const [priority, setPriority] = useState<TaskPriority>("medium");
  const [dueDate, setDueDate] = useState<string>("");
  const [newNote, setNewNote] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      if (task) {
        setTitle(task.title);
        setDescription(task.description ?? "");
        setColumnId(task.columnId);
        setAssignedTo(task.assignedTo ?? "");
        setAssignedTeam(task.assignedTeam ?? "All");
        setPriority(task.priority);
        setDueDate(
          task.dueDate ? format(task.dueDate.toDate(), "yyyy-MM-dd") : "",
        );
      } else {
        setTitle("");
        setDescription("");
        setColumnId(initialColumnId ?? columns[0]?.id ?? "");
        setAssignedTo("");
        setAssignedTeam("All");
        setPriority("medium");
        setDueDate("");
      }
      setNewNote("");
    }
  }, [isOpen, task, initialColumnId, columns]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !columnId) return;

    setLoading(true);
    try {
      const taskData: KanbanTask = {
        id: task?.id ?? "",
        title: title.trim(),
        description: description.trim() || "",
        columnId,
        assignedTo: assignedTo || null,
        assignedTeam: assignedTeam || "All",
        priority,
        dueDate: dueDate ? Timestamp.fromDate(new Date(dueDate)) : null,
        createdBy: task?.createdBy ?? "",
        createdAt: task?.createdAt ?? Timestamp.now(),
        updatedAt: Timestamp.now(),
        order: task?.order ?? 0,
        notes: task?.notes ?? [],
      };

      await onSave(taskData);
    } catch (error) {
      console.error("Failed to save task:", error);
    }
    setLoading(false);
  };

  const handleAddNote = async () => {
    if (!newNote.trim() || !task) return;
    try {
      await onAddNote(task.id, newNote.trim());
      setNewNote("");
    } catch (error) {
      console.error("Failed to add note:", error);
    }
  };

  const handleDelete = async () => {
    if (!task || !onDelete) return;
    const confirmed = window.confirm(
      "Are you sure you want to delete this task?",
    );
    if (confirmed) {
      setLoading(true);
      try {
        await onDelete(task.id);
      } catch (error) {
        console.error("Failed to delete task:", error);
      }
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="animate-in fade-in fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 duration-200">
      <div className="animate-in slide-in-from-bottom-4 m-4 max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-lg bg-secondary p-6 shadow-xl duration-300">
        <div className="mb-6 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-primary">
            {task ? "Edit Task" : "Create New Task"}
          </h2>
          <button
            aria-label="Close"
            onClick={onClose}
            className="text-secondary transition-colors hover:text-primary"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="md:col-span-2">
              <label className="mb-2 block text-sm font-medium text-primary">
                <FileText size={16} className="mr-2 inline" />
                Task Title
              </label>
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="w-full rounded-md border border-primary bg-primary px-3 py-2 text-primary transition-colors focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
                placeholder="Enter task title"
                required
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-primary">
                <Flag size={16} className="mr-2 inline" />
                Priority
              </label>
              <select
                aria-label="Priority Selector"
                value={priority}
                onChange={(e) => setPriority(e.target.value as TaskPriority)}
                className="w-full rounded-md border border-primary bg-primary px-3 py-2 text-primary transition-colors focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
              >
                {priorityOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.icon} {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-primary">
                <Calendar size={16} className="mr-2 inline" />
                Due Date
              </label>
              <input
                aria-label="Due Date"
                type="date"
                value={dueDate}
                onChange={(e) => setDueDate(e.target.value)}
                className="w-full rounded-md border border-primary bg-primary px-3 py-2 text-primary transition-colors focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-primary">
                Column
              </label>
              <select
                aria-label="Column Selector"
                value={columnId}
                onChange={(e) => setColumnId(e.target.value)}
                className="w-full rounded-md border border-primary bg-primary px-3 py-2 text-primary transition-colors focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
                required
              >
                {columns.map((column) => (
                  <option key={column.id} value={column.id}>
                    {column.title}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-primary">
                <User size={16} className="mr-2 inline" />
                Assign To
              </label>
              <select
                aria-label="User Selector"
                value={assignedTo}
                onChange={(e) => setAssignedTo(e.target.value)}
                className="w-full rounded-md border border-primary bg-primary px-3 py-2 text-primary transition-colors focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <option value="">Unassigned</option>
                {users.map((user) => (
                  <option key={user.uid} value={user.uid}>
                    {user.displayName ?? user.email}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-primary">
                <User size={16} className="mr-2 inline" />
                Assign To Team
              </label>
              <select
                aria-label="Team Selector"
                value={assignedTeam}
                onChange={(e) => setAssignedTeam(e.target.value)}
                className="w-full rounded-md border border-primary bg-primary px-3 py-2 text-primary transition-colors focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
              >
                <option value="All">All</option>
                <option value="Game Development">Game Development</option>
                <option value="3D Modeling">3D Modeling</option>
                <option value="Animation">Animation</option>
                <option value="Level Design">Level Design</option>
                <option value="Art">Art</option>
                <option value="Music">Music</option>
                <option value="VFX">VFX</option>
                <option value="Story Writing">Story Writing</option>
                <option value="Community Management">
                  Community Management
                </option>
                <option value="Finance">Finance</option>
                <option value="Legal">Legal</option>
                <option value="Marketing">Marketing</option>
                <option value="Operations">Operations</option>
                <option value="Human Resources">Human Resources</option>
              </select>
            </div>
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-primary">
              Description
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
              className="w-full rounded-md border border-primary bg-primary px-3 py-2 text-primary transition-colors focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
              placeholder="Task description (optional)"
            />
          </div>

          {task?.notes && task.notes.length > 0 && (
            <div>
              <h3 className="mb-3 text-sm font-medium text-primary">
                Notes ({task.notes.length})
              </h3>
              <div className="max-h-40 space-y-2 overflow-y-auto rounded-md bg-primary p-3">
                {task.notes.map((note) => (
                  <div key={note.id} className="rounded-md p-1">
                    <div className="mb-1 flex items-center justify-between">
                      <span className="text-xs font-medium text-primary">
                        {note.userName}
                      </span>
                      <span className="text-xs text-secondary">
                        {formatDistanceToNow(note.timestamp.toDate(), {
                          addSuffix: true,
                        })}
                      </span>
                    </div>
                    <p className="text-sm text-primary">{note.text}</p>
                  </div>
                ))}
              </div>
            </div>
          )}

          {task && (
            <div>
              <label className="mb-2 block text-sm font-medium text-primary">
                Add Note
              </label>
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newNote}
                  onChange={(e) => setNewNote(e.target.value)}
                  className="flex-1 rounded-md border border-primary bg-primary px-3 py-2 text-primary transition-colors focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
                  placeholder="Add a note..."
                />
                <button
                  aria-label="Add Note"
                  type="button"
                  onClick={handleAddNote}
                  disabled={!newNote.trim()}
                  className="rounded-md bg-accent px-4 py-2 text-white transition-opacity hover:opacity-90 disabled:opacity-50"
                >
                  <Plus size={16} />
                </button>
              </div>
            </div>
          )}

          <div className="flex justify-between pt-4">
            <div>
              {task && onDelete && (
                <button
                  type="button"
                  onClick={handleDelete}
                  disabled={loading}
                  className="rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white transition-colors hover:bg-red-700 disabled:opacity-50"
                >
                  Delete Task
                </button>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={onClose}
                className="rounded-md border border-primary bg-secondary px-4 py-2 text-sm font-semibold text-primary transition-colors hover:bg-primary/20"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading || !title.trim() || !columnId}
                className="rounded-md bg-accent px-4 py-2 text-sm font-semibold text-white transition-opacity hover:opacity-90 disabled:opacity-50"
              >
                {loading ? "Saving..." : task ? "Update Task" : "Create Task"}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskModal;