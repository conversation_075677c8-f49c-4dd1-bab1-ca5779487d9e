import React, { useEffect, useRef, useState } from "react";
import type { KanbanColumn, KanbanTask } from "@/types/kanban";
import TaskCard from "./TaskCard";
import { SortableContext, useSortable } from "@dnd-kit/sortable";
import { useDroppable } from "@dnd-kit/core";
import { CSS } from "@dnd-kit/utilities";
import { Edit, MoreVertical, PlusCircle, Trash2 } from "lucide-react";

interface ColumnProps {
  column: KanbanColumn;
  tasks: KanbanTask[];
  onTaskClick: (task: KanbanTask) => void;
  onAddTask: () => void;
  onEditColumn?: (column: KanbanColumn) => void;
  onDeleteColumn?: (columnId: string) => void;
  isAdmin: boolean;
  isDraggedOver?: boolean;
  activeDragItemId?: string;
}

const Column: React.FC<ColumnProps> = ({
                                         column,
                                         tasks,
                                         onTaskClick,
                                         onAddTask,
                                         onEditColumn,
                                         onDeleteColumn,
                                         isAdmin = false,
                                         isDraggedOver = false,
                                       }) => {
  const [showMenu, setShowMenu] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const {
    setNodeRef: setSortableNodeRef,
    attributes,
    listeners,
    transform,
    transition,
    isDragging,
    isOver: isSortableOver,
  } = useSortable({
    id: column.id,
    data: { type: "Column", column },
  });

  const { setNodeRef: setDroppableNodeRef, isOver: isDroppableOver } =
    useDroppable({
      id: `${column.id}-droppable`,
      data: { type: "Column", column },
    });

  const setNodeRef = (node: HTMLElement | null) => {
    setSortableNodeRef(node);
    setDroppableNodeRef(node);
  };

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? "none" : transition,
  };

  const validTasks = tasks.filter((task) => {
    return task?.id && task.id.trim() !== "";
  });

  const taskIds = validTasks.map((task) => task.id);

  const priorityCount = validTasks.reduce(
    (acc, task) => {
      acc[task.priority] = (acc[task.priority] ?? 0) + 1;
      return acc;
    },
    {} as Record<string, number>,
  );

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const isOver = isSortableOver || isDroppableOver;

  return (
    <div
      ref={setNodeRef}
      style={style}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={`flex h-full w-80 flex-shrink-0 flex-col rounded-lg p-4 transition-all duration-200 ${
        isDragging
          ? "rotate-1 scale-105 bg-primary opacity-50"
          : "bg-secondary"
      } ${
        isOver && !isDragging ? "bg-accent/10 ring-2 ring-accent" : ""
      } ${
        isDraggedOver && !isDragging
          ? "bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-300 scale-102"
          : ""
      } ${isHovered ? "shadow-lg" : "shadow-sm"} `}
    >
      <div className="mb-4 flex items-center justify-between border-b border-primary pb-3">
        <div
          {...attributes}
          {...listeners}
          className="flex-1 cursor-grab select-none transition-colors active:cursor-grabbing"
        >
          <h3 className="font-semibold text-primary">{column.title}</h3>
          <div className="mt-1 flex items-center gap-2">
            <span className="text-sm text-secondary">
              {validTasks.length} tasks
            </span>

            {priorityCount.critical && (
              <span className="rounded-full bg-red-100 px-2 py-0.5 text-xs text-red-700 dark:bg-red-900/30 dark:text-red-300">
                {priorityCount.critical} urgent
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => onAddTask()}
            className="text-accent transition-colors hover:opacity-80"
            aria-label={`Add task to ${column.title}`}
          >
            <PlusCircle size={20} />
          </button>

          {isAdmin && (onEditColumn ?? onDeleteColumn) && (
            <div className="relative" ref={menuRef}>
              <button
                aria-label="Open menu"
                onClick={() => setShowMenu(!showMenu)}
                className="text-secondary transition-colors hover:text-primary"
              >
                <MoreVertical size={20} />
              </button>

              {showMenu && (
                <div className="animate-in slide-in-from-top-2 absolute right-0 top-8 z-20 w-32 rounded-md border border-primary bg-secondary py-1 shadow-lg duration-200">
                  {onEditColumn && (
                    <button
                      onClick={() => {
                        onEditColumn(column);
                        setShowMenu(false);
                      }}
                      className="flex w-full items-center px-3 py-2 text-sm text-primary transition-colors hover:bg-primary/20"
                    >
                      <Edit size={16} className="mr-2" />
                      Edit
                    </button>
                  )}
                  {onDeleteColumn && (
                    <button
                      onClick={() => {
                        onDeleteColumn(column.id);
                        setShowMenu(false);
                      }}
                      className="flex w-full items-center px-3 py-2 text-sm text-red-600 transition-colors hover:bg-primary/20 dark:text-red-400"
                    >
                      <Trash2 size={16} className="mr-2" />
                      Delete
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      <div className="scrollbar-thin scrollbar-thumb-light-tan dark:scrollbar-thumb-soft-black scrollbar-track-transparent flex-grow space-y-3 overflow-y-auto pr-1">
        <SortableContext items={taskIds}>
          {validTasks.map((task) => (
            <TaskCard
              key={task.id}
              task={task}
              onClick={() => onTaskClick(task)}
            />
          ))}
        </SortableContext>
        {validTasks.length === 0 && (
          <div className="py-8 text-center">
            <p className="mb-2 text-sm text-secondary">No tasks yet</p>
            <button
              onClick={() => onAddTask()}
              className="text-xs text-accent transition-colors hover:opacity-80"
            >
              Add the first task
            </button>
          </div>
        )}

        {isDraggedOver && validTasks.length > 0 && (
          <div className="h-12 rounded border-2 border-dashed border-blue-300 bg-blue-50/50 dark:border-blue-600 dark:bg-blue-900/10">
            <div className="flex h-full items-center justify-center text-xs text-blue-600 dark:text-blue-400">
              Drop here to add to end
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Column;