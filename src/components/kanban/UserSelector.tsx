import React, { useCallback, useEffect, useState } from "react";
import type { UserProfile } from "@/types/kanban";
import { getAssignableUsers } from "@/lib/firebase/firestore";

interface UserSelectorProps {
  selectedUserId?: string | null;
  onChange: (userId: string | null) => void;
  disabled?: boolean;
}

const UserSelector: React.FC<UserSelectorProps> = ({
                                                     selectedUserId,
                                                     onChange,
                                                     disabled,
                                                   }) => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const fetchedUsers = await getAssignableUsers();
      const filteredUsers = fetchedUsers.filter(
        (user) => user.uid !== selectedUserId
      );
      setUsers(filteredUsers);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Failed to load users";
      console.error("Failed to fetch users:", errorMessage);
      setError("Failed to load users");
    } finally {
      setLoading(false);
    }
  }, [selectedUserId]);

  useEffect(() => {
    let isMounted = true;
    void fetchUsers().then(() => {
      if (process.env.NODE_ENV === 'development' && isMounted) {
        console.log("Users fetched successfully");
      }
    });
    return () => {
      isMounted = false;
    };
  }, [fetchUsers]);

  if (error) {
    return <p className="text-sm text-red-500">{error}</p>;
  }

  if (loading) {
    return <p className="text-sm text-gray-500">Loading users...</p>;
  }

  return (
    <select
      aria-label="User Selector"
      value={selectedUserId ?? ""}
      onChange={(e) => onChange(e.target.value || null)}
      disabled={disabled ?? users.length === 0}
      className="w-full rounded-md border border-gray-300 bg-white p-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white sm:text-sm"
    >
      <option value="">Unassigned</option>
      {users.map((user) => (
        <option key={user.uid} value={user.uid}>
          {user.displayName ?? user.email ?? user.uid}
        </option>
      ))}
    </select>
  );
};


export default UserSelector;
