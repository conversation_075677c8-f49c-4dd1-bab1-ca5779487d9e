"use client";

import React, { useCallback, useEffect, useMemo, useState } from "react";
import type {
  Active,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
} from "@dnd-kit/core";
import {
  closestCorners,
  DndContext,
  DragOverlay,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { arrayMove, SortableContext } from "@dnd-kit/sortable";
import { createPortal } from "react-dom";
import type { KanbanColumn, KanbanTask, UserProfile } from "@/types/kanban";
import {
  addColumn,
  addTask,
  addTaskNote as apiAddTaskNote,
  deleteColumn,
  deleteTask,
  deleteTasksByColumn,
  getAssignableUsers,
  getKanbanColumns,
  getKanbanTasks,
  updateColumn,
  updateColumnOrderBatch,
  updateTask,
  updateTaskOrderBatch,
} from "@/lib/firebase/firestore";
import { hasRole } from "@/lib/utils";
import Column from "./Column";
import TaskModal from "./TaskModal";
import ColumnModal from "./ColumnModal";
import TaskDragOverlay from "./TaskDragOverlay";
import { useAuth } from "@/lib/auth-context";
import { Columns, Filter, Plus, Search } from "lucide-react";
import { Timestamp } from "firebase/firestore";



const KanbanBoard: React.FC = () => {
  const { user, loading: authLoading } = useAuth();
  const isAuthenticated = !!user;
  const [columns, setColumns] = useState<KanbanColumn[]>([]);
  const [tasks, setTasks] = useState<KanbanTask[]>([]);
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<KanbanTask | null>(null);
  const [newTaskInitialColumnId, setNewTaskInitialColumnId] = useState<
    string | undefined
  >();

  const [isColumnModalOpen, setIsColumnModalOpen] = useState(false);
  const [selectedColumn, setSelectedColumn] = useState<KanbanColumn | null>(null);

  const [activeDragItem, setActiveDragItem] = useState<Active | null>(null);
  const [dragOverColumn, setDragOverColumn] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [userFilter, setUserFilter] = useState<string>("all");
  const [boardFilter, setBoardFilter] = useState<string>("all");

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
  );

  const isAdmin = useMemo(() => {
    return hasRole(user, "admin");
  }, [user]);

  const fetchData = useCallback(async () => {
    if (!isAuthenticated) return;
    setLoadingData(true);
    setError(null);
    try {
      const [fetchedColumns, fetchedTasks, fetchedUsers] = await Promise.all([
        getKanbanColumns(),
        getKanbanTasks(),
        getAssignableUsers(),
      ]);
      setColumns(fetchedColumns.sort((a, b) => a.order - b.order));
      setTasks(fetchedTasks);
      setUsers(fetchedUsers);
    } catch (err) {
      console.error("Failed to fetch kanban data:", err);
      setError("Failed to load board data. Please try again.");
    }
    setLoadingData(false);
  }, [isAuthenticated]); // Only depends on isAuthenticated

  useEffect(() => {
    if (isAuthenticated) {
      fetchData()
        .then(() => {
          // Consider using a logger or removing in production
          console.log("Fetching Data");
        })
        .catch((error) => {
          console.error("Error in fetching data:", error);
        });
    }
  }, [isAuthenticated, fetchData]);

  const filteredTasks = useMemo(() => {
    return tasks.filter((task) => {
      const matchesBoard = boardFilter === "all" || task.assignedTeam === boardFilter;
      const matchesSearch = task.title
        .toLowerCase()
        .includes(searchQuery.toLowerCase());
      const matchesPriority =
        priorityFilter === "all" || task.priority === priorityFilter;
      if (userFilter === "Not Assigned") {
        return matchesSearch && matchesPriority && !task.assignedTo;
      }
      const matchesUser = userFilter === "all" || task.assignedTo === userFilter;
      return matchesSearch && matchesPriority && matchesUser && matchesBoard;
    });
  }, [tasks, searchQuery, priorityFilter, userFilter, boardFilter]);

  const tasksByColumn = useMemo(() => {
    const grouped: Record<string, KanbanTask[]> = {};
    columns.forEach((col) => (grouped[col.id] = []));
    filteredTasks.forEach((task) => {
      if (grouped[task?.columnId]) {
        grouped[task?.columnId]?.push(task);
      } else {
        if (columns.length > 0 && columns[0])
          grouped[columns[0].id]?.push(task);
      }
    });
    for (const columnId in grouped) {
      grouped[columnId]?.sort((a, b) => a.order - b.order);
    }
    return grouped;
  }, [filteredTasks, columns]);

  const columnIds = useMemo(() => columns.map((col) => col.id), [columns]);

  const handleOpenTaskModal = (task?: KanbanTask, columnId?: string) => {
    setSelectedTask(task ?? null);
    setNewTaskInitialColumnId(columnId);
    setIsTaskModalOpen(true);
  };

  const handleCloseTaskModal = () => {
    setIsTaskModalOpen(false);
    setSelectedTask(null);
    setNewTaskInitialColumnId(undefined);
  };

  const handleSaveTask = async (taskData: KanbanTask) => {
    if (!user) return;
    try {
      const userMap = new Map(users.map((u) => [u.uid, u]));
      const assignedUser = taskData.assignedTo
        ? userMap.get(taskData.assignedTo)
        : null;
      const taskToSave = {
        ...taskData,
        assignedUserName:
          assignedUser?.displayName ?? assignedUser?.email ?? null,
      };

      if (taskData.id) {
        await updateTask(taskData.id, taskToSave);
        setTasks((prev) =>
          prev.map((t) =>
            t.id === taskData.id
              ? { ...t, ...taskToSave, updatedAt: Timestamp.now() }
              : t,
          ),
        );
      } else {
        const newTaskData = taskToSave;
        const creator = userMap.get(user.uid);
        const tasksInColumn = tasks.filter(
          (t) => t?.columnId === taskData?.columnId,
        );
        const newDbTask = {
          ...newTaskData,
          createdBy: user.uid,
          createdByName:
            creator?.displayName ?? creator?.email ?? user.email ?? "User",
          order: tasksInColumn.length,
        };
        const newId = await addTask(
          newDbTask as Omit<
            KanbanTask,
            "id" | "createdAt" | "updatedAt" | "notes"
          >,
        );
        setTasks((prev) => [
          ...prev,
          {
            ...newDbTask,
            id: newId,
            createdAt: Timestamp.now(),
            updatedAt: Timestamp.now(),
            notes: [],
          } as KanbanTask,
        ]);
      }
      handleCloseTaskModal();
    } catch (err) {
      console.error("Failed to save task:", err);
      alert("Failed to save task. Please try again.");
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      await deleteTask(taskId);
      setTasks((prev) => prev.filter((t) => t.id !== taskId));
      handleCloseTaskModal();
    } catch (err) {
      console.error("Failed to delete task:", err);
      alert("Failed to delete task. Please try again.");
    }
  };

  const handleAddTaskNote = async (taskId: string, noteText: string) => {
    if (!user) return;
    try {
      const userProfile = users.find((u) => u.uid === user.uid);
      const newNote = await apiAddTaskNote(
        taskId,
        noteText,
        user.uid,
        userProfile?.displayName ?? user.email ?? "User",
      );
      setTasks((prevTasks) =>
        prevTasks.map((t) => {
          if (t.id === taskId) {
            return {
              ...t,
              notes: [...(t.notes ?? []), newNote],
              updatedAt: Timestamp.now(),
            };
          }
          return t;
        }),
      );
      if (selectedTask && selectedTask.id === taskId) {
        setSelectedTask((prev) =>
          prev ? { ...prev, notes: [...(prev.notes ?? []), newNote] } : null,
        );
      }
    } catch (err) {
      console.error("Failed to add note:", err);
      throw err;
    }
  };

  const handleOpenColumnModal = (column?: KanbanColumn) => {
    setSelectedColumn(column ?? null);
    setIsColumnModalOpen(true);
  };

  const handleCloseColumnModal = () => {
    setIsColumnModalOpen(false);
    setSelectedColumn(null);
  };

  const handleSaveColumn = async (columnData: Omit<KanbanColumn, "id">) => {
    try {
      if (selectedColumn) {
        await updateColumn(selectedColumn.id, columnData);
        setColumns((prev) =>
          prev.map((col) =>
            col.id === selectedColumn.id ? { ...col, ...columnData } : col,
          ),
        );
      } else {
        const newId = await addColumn(columnData);
        setColumns((prev) => [...prev, { ...columnData, id: newId }]);
      }
      handleCloseColumnModal();
    } catch (err) {
      console.error("Failed to save column:", err);
      alert("Failed to save column. Please try again.");
    }
  };

  const handleDeleteColumn = async (columnId: string) => {
    const confirmDelete = window.confirm(
      "Are you sure you want to delete this column? This will also delete all tasks in this column.",
    );

    if (!confirmDelete) return;

    try {
      await deleteTasksByColumn(columnId);
      await deleteColumn(columnId);
      setColumns((prev) => prev.filter((col) => col.id !== columnId));
      setTasks((prev) => prev.filter((task) => task.columnId !== columnId));
    } catch (err) {
      console.error("Failed to delete column:", err);
      alert("Failed to delete column. Please try again.");
    }
  };




  const onDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const activeId = active.id as string;

    setActiveDragItem(active);
    setDragOverColumn(null); // Reset drag over column

    // Find the task or column being dragged
    const draggedTask = tasks.find((task) => task.id === activeId);
    const draggedColumn = columns.find((column) => column.id === activeId);

    if (draggedTask) {
      console.log("Started dragging task:", draggedTask.title, "from column:", draggedTask.columnId);
    } else if (draggedColumn) {
      console.log("Started dragging column:", draggedColumn.title);
    }
  };

  const onDragOver = (event: DragOverEvent) => {
    const { active, over } = event;

    if (!over || !active) {
      if (dragOverColumn) {
        setDragOverColumn(null);
      }
      return;
    }

    const overId = over.id as string;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const activeType = active.data?.current?.type;
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const overType = over.data?.current?.type;

    // Only track column changes for task drags
    if (activeType === "Task") {
      let targetColumnId: string | null = null;

      if (overType === "Column") {
        targetColumnId = overId;
      } else if (overType === "Task") {
        const overTask = tasks.find(t => t.id === overId);
        if (overTask) {
          targetColumnId = overTask.columnId;
        }
      } else {
        // Check if overId is a column ID or column droppable ID
        const isColumnId = columns.some(col => col.id === overId);
        const isColumnDroppable = overId.endsWith('-droppable');

        if (isColumnId) {
          targetColumnId = overId;
        } else if (isColumnDroppable) {
          targetColumnId = overId.replace('-droppable', '');
        }
      }

      if (targetColumnId !== dragOverColumn) {
        setDragOverColumn(targetColumnId);
      }
    }
  };

  const onDragEnd = async (event: DragEndEvent) => {
    setActiveDragItem(null);
    const targetColumn = dragOverColumn;
    setDragOverColumn(null);

    const { active, over } = event;

    if (!over) return;

    const activeId = active.id as string;
    const overId = over.id as string;

    // Validate IDs
    if (!activeId || !overId) {
      console.error("Invalid drag IDs:", { activeId, overId });
      return;
    }

    // Prevent any action if dropping on the same item
    if (activeId === overId) {
      console.log("Dropped on self, no action needed");
      return;
    }

    const isValidType = (type: unknown): type is "Task" | "Column" => {
      return type === "Task" || type === "Column";
    };

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const activeDataType = active.data?.current?.type;
    const activeType: "Task" | "Column" | undefined = isValidType(activeDataType)
      ? activeDataType
      : undefined;

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const overDataType = over.data?.current?.type;
    const overType: "Task" | "Column" | undefined = isValidType(overDataType)
      ? overDataType
      : undefined;

    // Handle column reordering
    if (activeType === "Column") {
      const oldIndex = columns.findIndex((col) => col.id === activeId);
      const newIndex = columns.findIndex((col) => col.id === overId);

      if (oldIndex === -1 || newIndex === -1 || oldIndex === newIndex) {
        return;
      }

      const newColumnsOrder = arrayMove(columns, oldIndex, newIndex);
      const updates = newColumnsOrder.map((col, index) => ({
        id: col.id,
        order: index,
      }));

      setColumns(newColumnsOrder);

      try {
        await updateColumnOrderBatch(updates);
        console.log("✅ Successfully updated column order");
      } catch (err) {
        console.error("❌ Failed to update column order:", err);
        await fetchData();
      }
      return;
    }

    // Handle task movement
    if (activeType === "Task") {
      const activeTask = tasks.find((t) => t.id === activeId);
      if (!activeTask) {
        console.error("Active task not found:", activeId);
        return;
      }

      let newColumnId = activeTask.columnId;

      // Determine target column
      if (targetColumn) {
        newColumnId = targetColumn;
      } else if (overType === "Column") {
        newColumnId = overId;
      } else if (overType === "Task") {
        const overTask = tasks.find((t) => t.id === overId);
        if (overTask) {
          newColumnId = overTask.columnId;
        }
      } else {
        // Handle droppable zones and column IDs
        const isColumnId = columns.some(col => col.id === overId);
        const isColumnDroppable = overId.endsWith('-droppable');

        if (isColumnId) {
          newColumnId = overId;
        } else if (isColumnDroppable) {
          newColumnId = overId.replace('-droppable', '');
        }
      }

      // Validate column ID
      if (!newColumnId || !columns.some(col => col.id === newColumnId)) {
        console.error("Invalid target column:", newColumnId);
        return;
      }

      console.log(`Moving task "${activeTask.title}" from "${activeTask.columnId}" to "${newColumnId}"`);

      const tasksToUpdate: { id: string; order: number; columnId?: string }[] = [];

      if (activeTask.columnId === newColumnId) {
        // Same column reordering
        const columnTasks = tasks
          .filter((t) => t.columnId === newColumnId)
          .sort((a, b) => a.order - b.order);

        const oldIndex = columnTasks.findIndex((t) => t.id === activeId);

        let newIndex = oldIndex;

        if (overType === "Task") {
          // Dropping on another task
          newIndex = columnTasks.findIndex((t) => t.id === overId);
        } else {
          // Dropping on column or empty space - move to end
          newIndex = columnTasks.length - 1;
        }

        if (oldIndex !== -1 && newIndex !== -1 && oldIndex !== newIndex) {
          const reorderedTasks = arrayMove(columnTasks, oldIndex, newIndex);
          reorderedTasks.forEach((task, index) => {
            if (task.order !== index && task.id) { // Validate task ID
              tasksToUpdate.push({ id: task.id, order: index });
            }
          });
        }
      } else {
        // Cross-column move
        const sourceColumnTasks = tasks
          .filter((t) => t.columnId === activeTask.columnId && t.id !== activeId)
          .sort((a, b) => a.order - b.order);

        const targetColumnTasks = tasks
          .filter((t) => t.columnId === newColumnId)
          .sort((a, b) => a.order - b.order);

        // Reorder source column (close the gap)
        sourceColumnTasks.forEach((task, index) => {
          if (task.order !== index && task.id) { // Validate task ID
            tasksToUpdate.push({ id: task.id, order: index });
          }
        });

        // Determine insertion point in target column
        let insertIndex = targetColumnTasks.length; // Default to end

        if (overType === "Task") {
          const overTaskIndex = targetColumnTasks.findIndex(t => t.id === overId);
          if (overTaskIndex !== -1) {
            insertIndex = overTaskIndex;
          }
        }

        // Shift tasks in target column to make room
        targetColumnTasks.forEach((task, index) => {
          const newOrder = index >= insertIndex ? index + 1 : index;
          if (task.order !== newOrder && task.id) { // Validate task ID
            tasksToUpdate.push({ id: task.id, order: newOrder });
          }
        });

        // Add the moved task at the insertion point
        if (activeTask.id) { // Validate active task ID
          tasksToUpdate.push({
            id: activeTask.id,
            order: insertIndex,
            columnId: newColumnId,
          });
        }
      }

      // Validate all task updates before processing
      const validTasksToUpdate = tasksToUpdate.filter(update =>
        update.id &&
        typeof update.id === 'string' &&
        update.id.trim() !== '' &&
        typeof update.order === 'number' &&
        update.order >= 0
      );

      if (validTasksToUpdate.length === 0) {
        console.error("No valid task updates to process");
        return;
      }

      if (validTasksToUpdate.length !== tasksToUpdate.length) {
        console.error("Some task updates were invalid:", {
          original: tasksToUpdate,
          valid: validTasksToUpdate
        });
      }

      console.log("Updating tasks:", validTasksToUpdate);

      // Optimistically update local state
      const updatedTasks = tasks.map((task) => {
        const update = validTasksToUpdate.find((u) => u.id === task.id);
        if (update) {
          return {
            ...task,
            order: update.order,
            columnId: update.columnId ?? task.columnId,
          };
        }
        return task;
      });

      setTasks(updatedTasks);

      try {
        await updateTaskOrderBatch(validTasksToUpdate);
        console.log("✅ Successfully updated task positions");
      } catch (err) {
        console.error("❌ Failed to update task order:", err);
        alert("Failed to move task. Please try again.");
        await fetchData();
      }
    }
  };

  if (authLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex h-screen items-center justify-center text-red-500">
        Please log in to view the Kanban board.
      </div>
    );
  }

  if (loadingData) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="animate-pulse space-y-4">
          <div className="h-8 w-64 rounded bg-gray-300"></div>
          <div className="flex space-x-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-96 w-80 rounded bg-gray-200"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen items-center justify-center text-red-500">
        {error}
      </div>
    );
  }

  const activeDragTask = activeDragItem?.data.current?.task as KanbanTask;

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCorners}
      onDragStart={onDragStart}
      onDragOver={onDragOver}
      onDragEnd={onDragEnd}
    >
      <div className="min-h-screen bg-primary p-4 md:p-6">
        <div className="mb-6 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <select
                aria-label="Board Filter"
                value={boardFilter}
                onChange={(e) => setBoardFilter(e.target.value)}
                className="rounded-lg border border-primary bg-secondary px-3 py-2 text-primary transition-all focus:border-transparent focus:ring-2 focus:ring-primary"
              >
                <option value="all">All TaskBoards</option>
                <option value="Game Development">Game Development</option>
                <option value="3D Modeling">3D Modeling</option>
                <option value="Animation">Animation</option>
                <option value="Level Design">Level Design</option>
                <option value="Art">Art</option>
                <option value="Music">Music</option>
                <option value="VFX">VFX</option>
                <option value="Story Writing">Story Writing</option>
                <option value="Community Management">
                  Community Management
                </option>
                <option value="Finance">Finance</option>
                <option value="Legal">Legal</option>
                <option value="Marketing">Marketing</option>
                <option value="Operations">Operations</option>
                <option value="Human Resources">Human Resources</option>
              </select>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() =>
                  handleOpenTaskModal(
                    undefined,
                    columns.length > 0 ? columns[0]?.id : undefined,
                  )
                }
                className="flex items-center rounded-lg bg-accent px-4 py-2 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-105 hover:opacity-90"
              >
                <Plus size={20} className="mr-2" /> Add Task
              </button>
              {isAdmin && (
                <button
                  onClick={() => handleOpenColumnModal()}
                  className="flex items-center rounded-lg border border-accent bg-transparent px-4 py-2 font-semibold text-accent shadow-lg transition-all duration-200 hover:scale-105 hover:bg-accent/10"
                >
                  <Columns size={20} className="mr-2" /> Add Column
                </button>
              )}
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-4">
            <div className="relative">
              <Search
                size={20}
                className="absolute left-3 top-1/2 -translate-y-1/2 transform text-secondary"
              />
              <input
                type="text"
                placeholder="Search tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64 rounded-lg border border-primary bg-secondary py-2 pl-10 pr-4 text-primary transition-all focus:border-transparent focus:ring-2 focus:ring-primary"
              />
            </div>

            <div className="flex items-center gap-2">
              <Filter size={20} className="text-secondary" />
              <select
                aria-label="Priority Filter"
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="rounded-lg border border-primary bg-secondary px-3 py-2 text-primary transition-all focus:border-transparent focus:ring-2 focus:ring-primary"
              >
                <option value="all">All Priorities</option>
                <option value="critical">🔴 Critical</option>
                <option value="high">🟠 High</option>
                <option value="medium">🟡 Medium</option>
                <option value="low">🟢 Low</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <Filter size={20} className="text-secondary" />
              <select
                aria-label="User Filter"
                value={userFilter}
                onChange={(e) => setUserFilter(e.target.value)}
                className="rounded-lg border border-primary bg-secondary px-3 py-2 text-primary transition-all focus:border-transparent focus:ring-2 focus:ring-primary"
              >
                <option value="all">All Users</option>
                <option value="Not Assigned">Unassigned</option>
                {users.map((user) => (
                  <option key={user.uid} value={user.uid}>
                    {user.displayName ?? user.email ?? user.uid}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {columns.length === 0 ? (
          <div className="py-20 text-center">
            <div className="mb-4 animate-bounce">
              <Columns size={64} className="mx-auto text-secondary" />
            </div>
            <p className="mb-4 text-lg text-secondary">
              No columns configured.
            </p>
            {isAdmin && (
              <button
                onClick={() => handleOpenColumnModal()}
                className="rounded-lg bg-accent px-6 py-3 font-semibold text-white shadow-lg transition-all duration-200 hover:scale-105 hover:opacity-90"
              >
                Create First Column
              </button>
            )}
          </div>
        ) : (
          <div className="scrollbar-thin scrollbar-thumb-light-tan dark:scrollbar-thumb-soft-black scrollbar-track-transparent flex space-x-6 overflow-x-auto pb-4">
            <SortableContext items={columnIds}>
              {columns.map((column) => (
                <Column
                  key={column.id}
                  column={column}
                  tasks={tasksByColumn[column.id] ?? []}
                  onTaskClick={(task) => handleOpenTaskModal(task)}
                  onAddTask={() => handleOpenTaskModal(undefined, column.id)}
                  onEditColumn={isAdmin ? handleOpenColumnModal : undefined}
                  onDeleteColumn={isAdmin ? handleDeleteColumn : undefined}
                  isAdmin={isAdmin}
                  isDraggedOver={dragOverColumn === column.id}
                  activeDragItemId={activeDragItem?.id as string}
                />
              ))}
            </SortableContext>
          </div>
        )}
      </div>

      {typeof window !== "undefined" &&
        createPortal(
          <DragOverlay>
            {activeDragTask && <TaskDragOverlay task={activeDragTask} />}
          </DragOverlay>,
          document.body,
        )}

      <TaskModal
        isOpen={isTaskModalOpen}
        onClose={handleCloseTaskModal}
        onSave={handleSaveTask}
        onDelete={handleDeleteTask}
        onAddNote={handleAddTaskNote}
        task={selectedTask}
        columns={columns}
        users={users}
        initialColumnId={newTaskInitialColumnId}
      />

      <ColumnModal
        isOpen={isColumnModalOpen}
        onClose={handleCloseColumnModal}
        onSave={handleSaveColumn}
        onDelete={isAdmin ? handleDeleteColumn : undefined}
        column={selectedColumn}
        existingColumns={columns}
      />
    </DndContext>
  );
};

export default KanbanBoard;