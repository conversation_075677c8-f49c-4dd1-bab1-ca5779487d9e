import React from "react";
import type { KanbanTask, TaskPriority } from "@/types/kanban";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import {
  Calendar,
  Flag,
  GripVertical,
  MessageSquare,
  UserCircle,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface TaskCardProps {
  task: KanbanTask;
  onClick: () => void;
  isDragOverlay?: boolean;
}

const priorityConfig: Record<
  TaskPriority,
  { color: string; borderColor: string; icon: string }
> = {
  low: {
    color: "text-green-600 dark:text-green-400",
    borderColor: "border-green-400 dark:border-green-600",
    icon: "🟢",
  },
  medium: {
    color: "text-yellow-600 dark:text-yellow-400",
    borderColor: "border-yellow-400 dark:border-yellow-600",
    icon: "🟡",
  },
  high: {
    color: "text-orange-600 dark:text-orange-400",
    borderColor: "border-orange-400 dark:border-orange-600",
    icon: "🟠",
  },
  critical: {
    color: "text-red-600 dark:text-red-400",
    borderColor: "border-red-400 dark:border-red-600",
    icon: "🔴",
  },
};

const TaskCard: React.FC<TaskCardProps> = ({
                                             task,
                                             onClick,
                                             isDragOverlay = false,
                                           }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: task.id,
    data: { type: "Task", task },
    disabled: isDragOverlay,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition: isDragging ? "none" : transition,
  };

  const priority = priorityConfig[task.priority];
  const isOverdue =
    task.dueDate && task.dueDate.toMillis() < Date.now() && task.dueDate;

  const handleClick = (e: React.MouseEvent) => {
    if (isDragging) {
      e.preventDefault();
      e.stopPropagation();
      return;
    }
    onClick();
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`group mb-3 cursor-pointer rounded-lg border-l-4 bg-primary p-3 shadow-sm transition-all duration-200 hover:scale-[1.02] hover:shadow-md hover:bg-secondary ${
        priority.borderColor
      } ${isDragging ? "rotate-3 scale-105 opacity-50" : ""} ${
        isDragOverlay ? "rotate-2 scale-105 shadow-2xl" : ""
      }`}
      onClick={handleClick}
    >
      <div className="flex items-start justify-between">
        <div className="min-w-0 flex-1">
          <div className="mb-2 flex items-center gap-2">
            <span className="text-lg leading-none">{priority.icon}</span>
            <h4 className="line-clamp-2 text-sm font-medium text-primary">
              {task.title}
            </h4>
          </div>

          {task.description && (
            <p className="mb-2 line-clamp-2 text-xs text-secondary">
              {task.description}
            </p>
          )}

          {task.dueDate && (
            <div
              className={`mb-2 flex items-center gap-1 text-xs ${
                isOverdue
                  ? "text-red-600 dark:text-red-400"
                  : "text-secondary"
              }`}
            >
              <Calendar size={12} />
              <span>
                {isOverdue ? "Overdue " : ""}
                {formatDistanceToNow(task.dueDate.toDate(), {
                  addSuffix: true,
                })}
              </span>
            </div>
          )}
        </div>

        <button
          {...attributes}
          {...listeners}
          className="touch-none p-1 text-secondary opacity-0 transition-opacity hover:text-primary group-hover:opacity-100"
          aria-label="Drag task"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          <GripVertical size={16} />
        </button>
      </div>

      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center gap-2">
          {task.assignedUserName && (
            <div className="flex items-center gap-1 text-secondary">
              <UserCircle size={12} />
              <span className="max-w-20 truncate">{task.assignedUserName}</span>
            </div>
          )}

          <div className={`flex items-center gap-1 ${priority.color}`}>
            <Flag size={12} />
            <span className="capitalize">{task.priority}</span>
          </div>
        </div>

        {task.notes && task.notes.length > 0 && (
          <div className="flex items-center gap-1 text-secondary">
            <MessageSquare size={12} />
            <span>{task.notes.length}</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskCard;