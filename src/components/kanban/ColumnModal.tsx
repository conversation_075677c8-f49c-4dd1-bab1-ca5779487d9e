"use client";

import React, { useEffect, useState } from "react";
import type { KanbanColumn } from "@/types/kanban";
import { X } from "lucide-react";

interface ColumnModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (columnData: Omit<KanbanColumn, "id">) => Promise<void>;
  onDelete?: (columnId: string) => Promise<void>;
  column?: KanbanColumn | null;
  existingColumns: KanbanColumn[];
}

const ColumnModal: React.FC<ColumnModalProps> = ({
                                                   isOpen,
                                                   onClose,
                                                   onSave,
                                                   onDelete,
                                                   column,
                                                   existingColumns,
                                                 }) => {
  const [title, setTitle] = useState("");
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setTitle(column?.title ?? "");
    }
  }, [isOpen, column]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim()) return;

    setLoading(true);
    try {
      const order = column?.order ?? existingColumns.length;
      await onSave({ title: title.trim(), order });
      onClose();
    } catch (error) {
      console.error("Failed to save column:", error);
      alert("Failed to save column. Please try again.");
    }
    setLoading(false);
  };

  const handleDelete = async () => {
    if (!column || !onDelete) return;

    const confirmDelete = window.confirm(
      `Are you sure you want to delete the "${column.title}" column? This will also delete all tasks in this column.`,
    );

    if (confirmDelete) {
      setLoading(true);
      try {
        await onDelete(column.id);
        onClose();
      } catch (error) {
        console.error("Failed to delete column:", error);
        alert("Failed to delete column. Please try again.");
      }
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-md rounded-lg bg-secondary p-6 shadow-xl">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-xl font-semibold text-primary">
            {column ? "Edit Column" : "Add Column"}
          </h2>
          <button
            aria-label="Close"
            onClick={onClose}
            className="text-secondary hover:text-primary"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-primary">
              Column Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="mt-1 block w-full rounded-md border border-primary bg-primary px-3 py-2 text-primary focus:border-accent focus:outline-none focus:ring-1 focus:ring-primary"
              placeholder="Enter column title"
              required
            />
          </div>

          <div className="flex justify-between pt-4">
            <div>
              {column && onDelete && (
                <button
                  type="button"
                  onClick={handleDelete}
                  disabled={loading}
                  className="rounded-md bg-red-600 px-4 py-2 text-sm font-semibold text-white hover:bg-red-700 disabled:opacity-50"
                >
                  {loading ? "Deleting..." : "Delete Column"}
                </button>
              )}
            </div>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={onClose}
                className="rounded-md border border-primary bg-secondary px-4 py-2 text-sm font-semibold text-primary hover:bg-primary/20"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading ?? !title.trim()}
                className="rounded-md bg-accent px-4 py-2 text-sm font-semibold text-white hover:opacity-90 disabled:opacity-50"
              >
                {loading ? "Saving..." : "Save"}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ColumnModal;