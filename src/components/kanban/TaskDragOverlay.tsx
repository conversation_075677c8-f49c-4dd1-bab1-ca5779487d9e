import React from "react";
import type { KanbanTask } from "@/types/kanban";
import TaskCard from "./TaskCard";

interface TaskDragOverlayProps {
  task: KanbanTask;
}

const TaskDragOverlay: React.FC<TaskDragOverlayProps> = ({ task }) => {
  const handleClick = () => {
    // No action needed for drag overlay
  };

  return (
    <div className="rotate-2 scale-105">
      <TaskCard task={task} onClick={handleClick} isDragOverlay />
    </div>
  );
};

export default TaskDragOverlay;
